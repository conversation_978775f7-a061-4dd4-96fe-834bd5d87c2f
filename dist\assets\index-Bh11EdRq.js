function Bb(l,u){for(var o=0;o<u.length;o++){const i=u[o];if(typeof i!="string"&&!Array.isArray(i)){for(const s in i)if(s!=="default"&&!(s in l)){const f=Object.getOwnPropertyDescriptor(i,s);f&&Object.defineProperty(l,s,f.get?f:{enumerable:!0,get:()=>i[s]})}}}return Object.freeze(Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}))}(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const f of s)if(f.type==="childList")for(const m of f.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&i(m)}).observe(document,{childList:!0,subtree:!0});function o(s){const f={};return s.integrity&&(f.integrity=s.integrity),s.referrerPolicy&&(f.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?f.credentials="include":s.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function i(s){if(s.ep)return;s.ep=!0;const f=o(s);fetch(s.href,f)}})();function Yp(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var Kc={exports:{}},xr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kh;function kb(){if(Kh)return xr;Kh=1;var l=Symbol.for("react.transitional.element"),u=Symbol.for("react.fragment");function o(i,s,f){var m=null;if(f!==void 0&&(m=""+f),s.key!==void 0&&(m=""+s.key),"key"in s){f={};for(var p in s)p!=="key"&&(f[p]=s[p])}else f=s;return s=f.ref,{$$typeof:l,type:i,key:m,ref:s!==void 0?s:null,props:f}}return xr.Fragment=u,xr.jsx=o,xr.jsxs=o,xr}var Ph;function Gb(){return Ph||(Ph=1,Kc.exports=kb()),Kc.exports}var _=Gb(),Pc={exports:{}},he={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $h;function Yb(){if($h)return he;$h=1;var l=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),m=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function E(w){return w===null||typeof w!="object"?null:(w=S&&w[S]||w["@@iterator"],typeof w=="function"?w:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,R={};function T(w,V,F){this.props=w,this.context=V,this.refs=R,this.updater=F||A}T.prototype.isReactComponent={},T.prototype.setState=function(w,V){if(typeof w!="object"&&typeof w!="function"&&w!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,w,V,"setState")},T.prototype.forceUpdate=function(w){this.updater.enqueueForceUpdate(this,w,"forceUpdate")};function N(){}N.prototype=T.prototype;function H(w,V,F){this.props=w,this.context=V,this.refs=R,this.updater=F||A}var k=H.prototype=new N;k.constructor=H,C(k,T.prototype),k.isPureReactComponent=!0;var X=Array.isArray,Y={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function $(w,V,F,P,I,de){return F=de.ref,{$$typeof:l,type:w,key:V,ref:F!==void 0?F:null,props:de}}function Z(w,V){return $(w.type,V,void 0,void 0,void 0,w.props)}function le(w){return typeof w=="object"&&w!==null&&w.$$typeof===l}function se(w){var V={"=":"=0",":":"=2"};return"$"+w.replace(/[=:]/g,function(F){return V[F]})}var be=/\/+/g;function fe(w,V){return typeof w=="object"&&w!==null&&w.key!=null?se(""+w.key):V.toString(36)}function Ee(){}function ge(w){switch(w.status){case"fulfilled":return w.value;case"rejected":throw w.reason;default:switch(typeof w.status=="string"?w.then(Ee,Ee):(w.status="pending",w.then(function(V){w.status==="pending"&&(w.status="fulfilled",w.value=V)},function(V){w.status==="pending"&&(w.status="rejected",w.reason=V)})),w.status){case"fulfilled":return w.value;case"rejected":throw w.reason}}throw w}function me(w,V,F,P,I){var de=typeof w;(de==="undefined"||de==="boolean")&&(w=null);var re=!1;if(w===null)re=!0;else switch(de){case"bigint":case"string":case"number":re=!0;break;case"object":switch(w.$$typeof){case l:case u:re=!0;break;case y:return re=w._init,me(re(w._payload),V,F,P,I)}}if(re)return I=I(w),re=P===""?"."+fe(w,0):P,X(I)?(F="",re!=null&&(F=re.replace(be,"$&/")+"/"),me(I,V,F,"",function(at){return at})):I!=null&&(le(I)&&(I=Z(I,F+(I.key==null||w&&w.key===I.key?"":(""+I.key).replace(be,"$&/")+"/")+re)),V.push(I)),1;re=0;var ue=P===""?".":P+":";if(X(w))for(var Ae=0;Ae<w.length;Ae++)P=w[Ae],de=ue+fe(P,Ae),re+=me(P,V,F,de,I);else if(Ae=E(w),typeof Ae=="function")for(w=Ae.call(w),Ae=0;!(P=w.next()).done;)P=P.value,de=ue+fe(P,Ae++),re+=me(P,V,F,de,I);else if(de==="object"){if(typeof w.then=="function")return me(ge(w),V,F,P,I);throw V=String(w),Error("Objects are not valid as a React child (found: "+(V==="[object Object]"?"object with keys {"+Object.keys(w).join(", ")+"}":V)+"). If you meant to render a collection of children, use an array instead.")}return re}function z(w,V,F){if(w==null)return w;var P=[],I=0;return me(w,P,"","",function(de){return V.call(F,de,I++)}),P}function K(w){if(w._status===-1){var V=w._result;V=V(),V.then(function(F){(w._status===0||w._status===-1)&&(w._status=1,w._result=F)},function(F){(w._status===0||w._status===-1)&&(w._status=2,w._result=F)}),w._status===-1&&(w._status=0,w._result=V)}if(w._status===1)return w._result.default;throw w._result}var B=typeof reportError=="function"?reportError:function(w){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var V=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof w=="object"&&w!==null&&typeof w.message=="string"?String(w.message):String(w),error:w});if(!window.dispatchEvent(V))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",w);return}console.error(w)};function J(){}return he.Children={map:z,forEach:function(w,V,F){z(w,function(){V.apply(this,arguments)},F)},count:function(w){var V=0;return z(w,function(){V++}),V},toArray:function(w){return z(w,function(V){return V})||[]},only:function(w){if(!le(w))throw Error("React.Children.only expected to receive a single React element child.");return w}},he.Component=T,he.Fragment=o,he.Profiler=s,he.PureComponent=H,he.StrictMode=i,he.Suspense=v,he.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Y,he.__COMPILER_RUNTIME={__proto__:null,c:function(w){return Y.H.useMemoCache(w)}},he.cache=function(w){return function(){return w.apply(null,arguments)}},he.cloneElement=function(w,V,F){if(w==null)throw Error("The argument must be a React element, but you passed "+w+".");var P=C({},w.props),I=w.key,de=void 0;if(V!=null)for(re in V.ref!==void 0&&(de=void 0),V.key!==void 0&&(I=""+V.key),V)!W.call(V,re)||re==="key"||re==="__self"||re==="__source"||re==="ref"&&V.ref===void 0||(P[re]=V[re]);var re=arguments.length-2;if(re===1)P.children=F;else if(1<re){for(var ue=Array(re),Ae=0;Ae<re;Ae++)ue[Ae]=arguments[Ae+2];P.children=ue}return $(w.type,I,void 0,void 0,de,P)},he.createContext=function(w){return w={$$typeof:m,_currentValue:w,_currentValue2:w,_threadCount:0,Provider:null,Consumer:null},w.Provider=w,w.Consumer={$$typeof:f,_context:w},w},he.createElement=function(w,V,F){var P,I={},de=null;if(V!=null)for(P in V.key!==void 0&&(de=""+V.key),V)W.call(V,P)&&P!=="key"&&P!=="__self"&&P!=="__source"&&(I[P]=V[P]);var re=arguments.length-2;if(re===1)I.children=F;else if(1<re){for(var ue=Array(re),Ae=0;Ae<re;Ae++)ue[Ae]=arguments[Ae+2];I.children=ue}if(w&&w.defaultProps)for(P in re=w.defaultProps,re)I[P]===void 0&&(I[P]=re[P]);return $(w,de,void 0,void 0,null,I)},he.createRef=function(){return{current:null}},he.forwardRef=function(w){return{$$typeof:p,render:w}},he.isValidElement=le,he.lazy=function(w){return{$$typeof:y,_payload:{_status:-1,_result:w},_init:K}},he.memo=function(w,V){return{$$typeof:h,type:w,compare:V===void 0?null:V}},he.startTransition=function(w){var V=Y.T,F={};Y.T=F;try{var P=w(),I=Y.S;I!==null&&I(F,P),typeof P=="object"&&P!==null&&typeof P.then=="function"&&P.then(J,B)}catch(de){B(de)}finally{Y.T=V}},he.unstable_useCacheRefresh=function(){return Y.H.useCacheRefresh()},he.use=function(w){return Y.H.use(w)},he.useActionState=function(w,V,F){return Y.H.useActionState(w,V,F)},he.useCallback=function(w,V){return Y.H.useCallback(w,V)},he.useContext=function(w){return Y.H.useContext(w)},he.useDebugValue=function(){},he.useDeferredValue=function(w,V){return Y.H.useDeferredValue(w,V)},he.useEffect=function(w,V,F){var P=Y.H;if(typeof F=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return P.useEffect(w,V)},he.useId=function(){return Y.H.useId()},he.useImperativeHandle=function(w,V,F){return Y.H.useImperativeHandle(w,V,F)},he.useInsertionEffect=function(w,V){return Y.H.useInsertionEffect(w,V)},he.useLayoutEffect=function(w,V){return Y.H.useLayoutEffect(w,V)},he.useMemo=function(w,V){return Y.H.useMemo(w,V)},he.useOptimistic=function(w,V){return Y.H.useOptimistic(w,V)},he.useReducer=function(w,V,F){return Y.H.useReducer(w,V,F)},he.useRef=function(w){return Y.H.useRef(w)},he.useState=function(w){return Y.H.useState(w)},he.useSyncExternalStore=function(w,V,F){return Y.H.useSyncExternalStore(w,V,F)},he.useTransition=function(){return Y.H.useTransition()},he.version="19.1.1",he}var Jh;function Cs(){return Jh||(Jh=1,Pc.exports=Yb()),Pc.exports}var g=Cs();const Fn=Yp(g),qp=Bb({__proto__:null,default:Fn},[g]);var $c={exports:{}},Er={},Jc={exports:{}},Fc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fh;function qb(){return Fh||(Fh=1,(function(l){function u(z,K){var B=z.length;z.push(K);e:for(;0<B;){var J=B-1>>>1,w=z[J];if(0<s(w,K))z[J]=K,z[B]=w,B=J;else break e}}function o(z){return z.length===0?null:z[0]}function i(z){if(z.length===0)return null;var K=z[0],B=z.pop();if(B!==K){z[0]=B;e:for(var J=0,w=z.length,V=w>>>1;J<V;){var F=2*(J+1)-1,P=z[F],I=F+1,de=z[I];if(0>s(P,B))I<w&&0>s(de,P)?(z[J]=de,z[I]=B,J=I):(z[J]=P,z[F]=B,J=F);else if(I<w&&0>s(de,B))z[J]=de,z[I]=B,J=I;else break e}}return K}function s(z,K){var B=z.sortIndex-K.sortIndex;return B!==0?B:z.id-K.id}if(l.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;l.unstable_now=function(){return f.now()}}else{var m=Date,p=m.now();l.unstable_now=function(){return m.now()-p}}var v=[],h=[],y=1,S=null,E=3,A=!1,C=!1,R=!1,T=!1,N=typeof setTimeout=="function"?setTimeout:null,H=typeof clearTimeout=="function"?clearTimeout:null,k=typeof setImmediate<"u"?setImmediate:null;function X(z){for(var K=o(h);K!==null;){if(K.callback===null)i(h);else if(K.startTime<=z)i(h),K.sortIndex=K.expirationTime,u(v,K);else break;K=o(h)}}function Y(z){if(R=!1,X(z),!C)if(o(v)!==null)C=!0,W||(W=!0,fe());else{var K=o(h);K!==null&&me(Y,K.startTime-z)}}var W=!1,$=-1,Z=5,le=-1;function se(){return T?!0:!(l.unstable_now()-le<Z)}function be(){if(T=!1,W){var z=l.unstable_now();le=z;var K=!0;try{e:{C=!1,R&&(R=!1,H($),$=-1),A=!0;var B=E;try{t:{for(X(z),S=o(v);S!==null&&!(S.expirationTime>z&&se());){var J=S.callback;if(typeof J=="function"){S.callback=null,E=S.priorityLevel;var w=J(S.expirationTime<=z);if(z=l.unstable_now(),typeof w=="function"){S.callback=w,X(z),K=!0;break t}S===o(v)&&i(v),X(z)}else i(v);S=o(v)}if(S!==null)K=!0;else{var V=o(h);V!==null&&me(Y,V.startTime-z),K=!1}}break e}finally{S=null,E=B,A=!1}K=void 0}}finally{K?fe():W=!1}}}var fe;if(typeof k=="function")fe=function(){k(be)};else if(typeof MessageChannel<"u"){var Ee=new MessageChannel,ge=Ee.port2;Ee.port1.onmessage=be,fe=function(){ge.postMessage(null)}}else fe=function(){N(be,0)};function me(z,K){$=N(function(){z(l.unstable_now())},K)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(z){z.callback=null},l.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Z=0<z?Math.floor(1e3/z):5},l.unstable_getCurrentPriorityLevel=function(){return E},l.unstable_next=function(z){switch(E){case 1:case 2:case 3:var K=3;break;default:K=E}var B=E;E=K;try{return z()}finally{E=B}},l.unstable_requestPaint=function(){T=!0},l.unstable_runWithPriority=function(z,K){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var B=E;E=z;try{return K()}finally{E=B}},l.unstable_scheduleCallback=function(z,K,B){var J=l.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?J+B:J):B=J,z){case 1:var w=-1;break;case 2:w=250;break;case 5:w=1073741823;break;case 4:w=1e4;break;default:w=5e3}return w=B+w,z={id:y++,callback:K,priorityLevel:z,startTime:B,expirationTime:w,sortIndex:-1},B>J?(z.sortIndex=B,u(h,z),o(v)===null&&z===o(h)&&(R?(H($),$=-1):R=!0,me(Y,B-J))):(z.sortIndex=w,u(v,z),C||A||(C=!0,W||(W=!0,fe()))),z},l.unstable_shouldYield=se,l.unstable_wrapCallback=function(z){var K=E;return function(){var B=E;E=K;try{return z.apply(this,arguments)}finally{E=B}}}})(Fc)),Fc}var Wh;function Vb(){return Wh||(Wh=1,Jc.exports=qb()),Jc.exports}var Wc={exports:{}},lt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ih;function Xb(){if(Ih)return lt;Ih=1;var l=Cs();function u(v){var h="https://react.dev/errors/"+v;if(1<arguments.length){h+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)h+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+v+"; visit "+h+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var i={d:{f:o,r:function(){throw Error(u(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(v,h,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:S==null?null:""+S,children:v,containerInfo:h,implementation:y}}var m=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(v,h){if(v==="font")return"";if(typeof h=="string")return h==="use-credentials"?h:""}return lt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,lt.createPortal=function(v,h){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!h||h.nodeType!==1&&h.nodeType!==9&&h.nodeType!==11)throw Error(u(299));return f(v,h,null,y)},lt.flushSync=function(v){var h=m.T,y=i.p;try{if(m.T=null,i.p=2,v)return v()}finally{m.T=h,i.p=y,i.d.f()}},lt.preconnect=function(v,h){typeof v=="string"&&(h?(h=h.crossOrigin,h=typeof h=="string"?h==="use-credentials"?h:"":void 0):h=null,i.d.C(v,h))},lt.prefetchDNS=function(v){typeof v=="string"&&i.d.D(v)},lt.preinit=function(v,h){if(typeof v=="string"&&h&&typeof h.as=="string"){var y=h.as,S=p(y,h.crossOrigin),E=typeof h.integrity=="string"?h.integrity:void 0,A=typeof h.fetchPriority=="string"?h.fetchPriority:void 0;y==="style"?i.d.S(v,typeof h.precedence=="string"?h.precedence:void 0,{crossOrigin:S,integrity:E,fetchPriority:A}):y==="script"&&i.d.X(v,{crossOrigin:S,integrity:E,fetchPriority:A,nonce:typeof h.nonce=="string"?h.nonce:void 0})}},lt.preinitModule=function(v,h){if(typeof v=="string")if(typeof h=="object"&&h!==null){if(h.as==null||h.as==="script"){var y=p(h.as,h.crossOrigin);i.d.M(v,{crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0})}}else h==null&&i.d.M(v)},lt.preload=function(v,h){if(typeof v=="string"&&typeof h=="object"&&h!==null&&typeof h.as=="string"){var y=h.as,S=p(y,h.crossOrigin);i.d.L(v,y,{crossOrigin:S,integrity:typeof h.integrity=="string"?h.integrity:void 0,nonce:typeof h.nonce=="string"?h.nonce:void 0,type:typeof h.type=="string"?h.type:void 0,fetchPriority:typeof h.fetchPriority=="string"?h.fetchPriority:void 0,referrerPolicy:typeof h.referrerPolicy=="string"?h.referrerPolicy:void 0,imageSrcSet:typeof h.imageSrcSet=="string"?h.imageSrcSet:void 0,imageSizes:typeof h.imageSizes=="string"?h.imageSizes:void 0,media:typeof h.media=="string"?h.media:void 0})}},lt.preloadModule=function(v,h){if(typeof v=="string")if(h){var y=p(h.as,h.crossOrigin);i.d.m(v,{as:typeof h.as=="string"&&h.as!=="script"?h.as:void 0,crossOrigin:y,integrity:typeof h.integrity=="string"?h.integrity:void 0})}else i.d.m(v)},lt.requestFormReset=function(v){i.d.r(v)},lt.unstable_batchedUpdates=function(v,h){return v(h)},lt.useFormState=function(v,h,y){return m.H.useFormState(v,h,y)},lt.useFormStatus=function(){return m.H.useHostTransitionStatus()},lt.version="19.1.1",lt}var ep;function Vp(){if(ep)return Wc.exports;ep=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(u){console.error(u)}}return l(),Wc.exports=Xb(),Wc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tp;function Qb(){if(tp)return Er;tp=1;var l=Vb(),u=Cs(),o=Vp();function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(f(e)!==e)throw Error(i(188))}function v(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(i(188));return t!==e?null:e}for(var n=e,a=t;;){var r=n.return;if(r===null)break;var c=r.alternate;if(c===null){if(a=r.return,a!==null){n=a;continue}break}if(r.child===c.child){for(c=r.child;c;){if(c===n)return p(r),e;if(c===a)return p(r),t;c=c.sibling}throw Error(i(188))}if(n.return!==a.return)n=r,a=c;else{for(var d=!1,b=r.child;b;){if(b===n){d=!0,n=r,a=c;break}if(b===a){d=!0,a=r,n=c;break}b=b.sibling}if(!d){for(b=c.child;b;){if(b===n){d=!0,n=c,a=r;break}if(b===a){d=!0,a=c,n=r;break}b=b.sibling}if(!d)throw Error(i(189))}}if(n.alternate!==a)throw Error(i(190))}if(n.tag!==3)throw Error(i(188));return n.stateNode.current===n?e:t}function h(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=h(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),E=Symbol.for("react.transitional.element"),A=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),R=Symbol.for("react.strict_mode"),T=Symbol.for("react.profiler"),N=Symbol.for("react.provider"),H=Symbol.for("react.consumer"),k=Symbol.for("react.context"),X=Symbol.for("react.forward_ref"),Y=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),$=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),le=Symbol.for("react.activity"),se=Symbol.for("react.memo_cache_sentinel"),be=Symbol.iterator;function fe(e){return e===null||typeof e!="object"?null:(e=be&&e[be]||e["@@iterator"],typeof e=="function"?e:null)}var Ee=Symbol.for("react.client.reference");function ge(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Ee?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case C:return"Fragment";case T:return"Profiler";case R:return"StrictMode";case Y:return"Suspense";case W:return"SuspenseList";case le:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case A:return"Portal";case k:return(e.displayName||"Context")+".Provider";case H:return(e._context.displayName||"Context")+".Consumer";case X:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case $:return t=e.displayName||null,t!==null?t:ge(e.type)||"Memo";case Z:t=e._payload,e=e._init;try{return ge(e(t))}catch{}}return null}var me=Array.isArray,z=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B={pending:!1,data:null,method:null,action:null},J=[],w=-1;function V(e){return{current:e}}function F(e){0>w||(e.current=J[w],J[w]=null,w--)}function P(e,t){w++,J[w]=e.current,e.current=t}var I=V(null),de=V(null),re=V(null),ue=V(null);function Ae(e,t){switch(P(re,t),P(de,e),P(I,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?xh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=xh(t),e=Eh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(I),P(I,e)}function at(){F(I),F(de),F(re)}function Gt(e){e.memoizedState!==null&&P(ue,e);var t=I.current,n=Eh(t,e.type);t!==n&&(P(de,e),P(I,n))}function Yt(e){de.current===e&&(F(I),F(de)),ue.current===e&&(F(ue),vr._currentValue=B)}var qt=Object.prototype.hasOwnProperty,An=l.unstable_scheduleCallback,Ui=l.unstable_cancelCallback,vy=l.unstable_shouldYield,gy=l.unstable_requestPaint,Vt=l.unstable_now,yy=l.unstable_getCurrentPriorityLevel,ef=l.unstable_ImmediatePriority,tf=l.unstable_UserBlockingPriority,Yr=l.unstable_NormalPriority,by=l.unstable_LowPriority,nf=l.unstable_IdlePriority,Sy=l.log,xy=l.unstable_setDisableYieldValue,Ra=null,ht=null;function Mn(e){if(typeof Sy=="function"&&xy(e),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode(Ra,e)}catch{}}var pt=Math.clz32?Math.clz32:Ry,Ey=Math.log,wy=Math.LN2;function Ry(e){return e>>>=0,e===0?32:31-(Ey(e)/wy|0)|0}var qr=256,Vr=4194304;function nl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Xr(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var r=0,c=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var b=a&134217727;return b!==0?(a=b&~c,a!==0?r=nl(a):(d&=b,d!==0?r=nl(d):n||(n=b&~e,n!==0&&(r=nl(n))))):(b=a&~c,b!==0?r=nl(b):d!==0?r=nl(d):n||(n=a&~e,n!==0&&(r=nl(n)))),r===0?0:t!==0&&t!==r&&(t&c)===0&&(c=r&-r,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:r}function Aa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Ay(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lf(){var e=qr;return qr<<=1,(qr&4194048)===0&&(qr=256),e}function af(){var e=Vr;return Vr<<=1,(Vr&62914560)===0&&(Vr=4194304),e}function Li(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ma(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function My(e,t,n,a,r,c){var d=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var b=e.entanglements,x=e.expirationTimes,U=e.hiddenUpdates;for(n=d&~n;0<n;){var G=31-pt(n),Q=1<<G;b[G]=0,x[G]=-1;var L=U[G];if(L!==null)for(U[G]=null,G=0;G<L.length;G++){var j=L[G];j!==null&&(j.lane&=-536870913)}n&=~Q}a!==0&&rf(e,a,0),c!==0&&r===0&&e.tag!==0&&(e.suspendedLanes|=c&~(d&~t))}function rf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-pt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function uf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-pt(n),r=1<<a;r&t|e[a]&t&&(e[a]|=t),n&=~r}}function ji(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Hi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function of(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:Yh(e.type))}function Ty(e,t){var n=K.p;try{return K.p=e,t()}finally{K.p=n}}var Tn=Math.random().toString(36).slice(2),tt="__reactFiber$"+Tn,ot="__reactProps$"+Tn,Ml="__reactContainer$"+Tn,Bi="__reactEvents$"+Tn,Cy="__reactListeners$"+Tn,Oy="__reactHandles$"+Tn,cf="__reactResources$"+Tn,Ta="__reactMarker$"+Tn;function ki(e){delete e[tt],delete e[ot],delete e[Bi],delete e[Cy],delete e[Oy]}function Tl(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ml]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Mh(e);e!==null;){if(n=e[tt])return n;e=Mh(e)}return t}e=n,n=e.parentNode}return null}function Cl(e){if(e=e[tt]||e[Ml]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ca(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(i(33))}function Ol(e){var t=e[cf];return t||(t=e[cf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ke(e){e[Ta]=!0}var sf=new Set,ff={};function ll(e,t){_l(e,t),_l(e+"Capture",t)}function _l(e,t){for(ff[e]=t,e=0;e<t.length;e++)sf.add(t[e])}var _y=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),df={},mf={};function Dy(e){return qt.call(mf,e)?!0:qt.call(df,e)?!1:_y.test(e)?mf[e]=!0:(df[e]=!0,!1)}function Qr(e,t,n){if(Dy(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Zr(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function nn(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+a)}}var Gi,hf;function Dl(e){if(Gi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Gi=t&&t[1]||"",hf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Gi+e+hf}var Yi=!1;function qi(e,t){if(!e||Yi)return"";Yi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(j){var L=j}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(j){L=j}e.call(Q.prototype)}}else{try{throw Error()}catch(j){L=j}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(j){if(j&&L&&typeof j.stack=="string")return[j.stack,L.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=a.DetermineComponentFrameRoot(),d=c[0],b=c[1];if(d&&b){var x=d.split(`
`),U=b.split(`
`);for(r=a=0;a<x.length&&!x[a].includes("DetermineComponentFrameRoot");)a++;for(;r<U.length&&!U[r].includes("DetermineComponentFrameRoot");)r++;if(a===x.length||r===U.length)for(a=x.length-1,r=U.length-1;1<=a&&0<=r&&x[a]!==U[r];)r--;for(;1<=a&&0<=r;a--,r--)if(x[a]!==U[r]){if(a!==1||r!==1)do if(a--,r--,0>r||x[a]!==U[r]){var G=`
`+x[a].replace(" at new "," at ");return e.displayName&&G.includes("<anonymous>")&&(G=G.replace("<anonymous>",e.displayName)),G}while(1<=a&&0<=r);break}}}finally{Yi=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Dl(n):""}function Ny(e){switch(e.tag){case 26:case 27:case 5:return Dl(e.type);case 16:return Dl("Lazy");case 13:return Dl("Suspense");case 19:return Dl("SuspenseList");case 0:case 15:return qi(e.type,!1);case 11:return qi(e.type.render,!1);case 1:return qi(e.type,!0);case 31:return Dl("Activity");default:return""}}function pf(e){try{var t="";do t+=Ny(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function At(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function vf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function zy(e){var t=vf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var r=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(d){a=""+d,c.call(this,d)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(d){a=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Kr(e){e._valueTracker||(e._valueTracker=zy(e))}function gf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=vf(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function Pr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Uy=/[\n"\\]/g;function Mt(e){return e.replace(Uy,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Vi(e,t,n,a,r,c,d,b){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+At(t)):e.value!==""+At(t)&&(e.value=""+At(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?Xi(e,d,At(t)):n!=null?Xi(e,d,At(n)):a!=null&&e.removeAttribute("value"),r==null&&c!=null&&(e.defaultChecked=!!c),r!=null&&(e.checked=r&&typeof r!="function"&&typeof r!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+At(b):e.removeAttribute("name")}function yf(e,t,n,a,r,c,d,b){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+At(n):"",t=t!=null?""+At(t):n,b||t===e.value||(e.value=t),e.defaultValue=t}a=a??r,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=b?e.checked:!!a,e.defaultChecked=!!a,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function Xi(e,t,n){t==="number"&&Pr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Nl(e,t,n,a){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&a&&(e[n].defaultSelected=!0)}else{for(n=""+At(n),t=null,r=0;r<e.length;r++){if(e[r].value===n){e[r].selected=!0,a&&(e[r].defaultSelected=!0);return}t!==null||e[r].disabled||(t=e[r])}t!==null&&(t.selected=!0)}}function bf(e,t,n){if(t!=null&&(t=""+At(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+At(n):""}function Sf(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error(i(92));if(me(a)){if(1<a.length)throw Error(i(93));a=a[0]}n=a}n==null&&(n=""),t=n}n=At(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function zl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ly=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function xf(e,t,n){var a=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||Ly.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ef(e,t,n){if(t!=null&&typeof t!="object")throw Error(i(62));if(e=e.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var r in t)a=t[r],t.hasOwnProperty(r)&&n[r]!==a&&xf(e,r,a)}else for(var c in t)t.hasOwnProperty(c)&&xf(e,c,t[c])}function Qi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var jy=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Hy=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function $r(e){return Hy.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Zi=null;function Ki(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ul=null,Ll=null;function wf(e){var t=Cl(e);if(t&&(e=t.stateNode)){var n=e[ot]||null;e:switch(e=t.stateNode,t.type){case"input":if(Vi(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var r=a[ot]||null;if(!r)throw Error(i(90));Vi(a,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&gf(a)}break e;case"textarea":bf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Nl(e,!!n.multiple,t,!1)}}}var Pi=!1;function Rf(e,t,n){if(Pi)return e(t,n);Pi=!0;try{var a=e(t);return a}finally{if(Pi=!1,(Ul!==null||Ll!==null)&&(Uu(),Ul&&(t=Ul,e=Ll,Ll=Ul=null,wf(t),e)))for(t=0;t<e.length;t++)wf(e[t])}}function Oa(e,t){var n=e.stateNode;if(n===null)return null;var a=n[ot]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(i(231,t,typeof n));return n}var ln=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),$i=!1;if(ln)try{var _a={};Object.defineProperty(_a,"passive",{get:function(){$i=!0}}),window.addEventListener("test",_a,_a),window.removeEventListener("test",_a,_a)}catch{$i=!1}var Cn=null,Ji=null,Jr=null;function Af(){if(Jr)return Jr;var e,t=Ji,n=t.length,a,r="value"in Cn?Cn.value:Cn.textContent,c=r.length;for(e=0;e<n&&t[e]===r[e];e++);var d=n-e;for(a=1;a<=d&&t[n-a]===r[c-a];a++);return Jr=r.slice(e,1<a?1-a:void 0)}function Fr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Wr(){return!0}function Mf(){return!1}function ct(e){function t(n,a,r,c,d){this._reactName=n,this._targetInst=r,this.type=a,this.nativeEvent=c,this.target=d,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(n=e[b],this[b]=n?n(c):c[b]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Wr:Mf,this.isPropagationStopped=Mf,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Wr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Wr)},persist:function(){},isPersistent:Wr}),t}var al={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ir=ct(al),Da=y({},al,{view:0,detail:0}),By=ct(Da),Fi,Wi,Na,eu=y({},Da,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:eo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Na&&(Na&&e.type==="mousemove"?(Fi=e.screenX-Na.screenX,Wi=e.screenY-Na.screenY):Wi=Fi=0,Na=e),Fi)},movementY:function(e){return"movementY"in e?e.movementY:Wi}}),Tf=ct(eu),ky=y({},eu,{dataTransfer:0}),Gy=ct(ky),Yy=y({},Da,{relatedTarget:0}),Ii=ct(Yy),qy=y({},al,{animationName:0,elapsedTime:0,pseudoElement:0}),Vy=ct(qy),Xy=y({},al,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qy=ct(Xy),Zy=y({},al,{data:0}),Cf=ct(Zy),Ky={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Py={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$y={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Jy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=$y[e])?!!t[e]:!1}function eo(){return Jy}var Fy=y({},Da,{key:function(e){if(e.key){var t=Ky[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Py[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:eo,charCode:function(e){return e.type==="keypress"?Fr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Wy=ct(Fy),Iy=y({},eu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Of=ct(Iy),e0=y({},Da,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:eo}),t0=ct(e0),n0=y({},al,{propertyName:0,elapsedTime:0,pseudoElement:0}),l0=ct(n0),a0=y({},eu,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),r0=ct(a0),u0=y({},al,{newState:0,oldState:0}),i0=ct(u0),o0=[9,13,27,32],to=ln&&"CompositionEvent"in window,za=null;ln&&"documentMode"in document&&(za=document.documentMode);var c0=ln&&"TextEvent"in window&&!za,_f=ln&&(!to||za&&8<za&&11>=za),Df=" ",Nf=!1;function zf(e,t){switch(e){case"keyup":return o0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Uf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var jl=!1;function s0(e,t){switch(e){case"compositionend":return Uf(t);case"keypress":return t.which!==32?null:(Nf=!0,Df);case"textInput":return e=t.data,e===Df&&Nf?null:e;default:return null}}function f0(e,t){if(jl)return e==="compositionend"||!to&&zf(e,t)?(e=Af(),Jr=Ji=Cn=null,jl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return _f&&t.locale!=="ko"?null:t.data;default:return null}}var d0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Lf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!d0[e.type]:t==="textarea"}function jf(e,t,n,a){Ul?Ll?Ll.push(a):Ll=[a]:Ul=a,t=Gu(t,"onChange"),0<t.length&&(n=new Ir("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var Ua=null,La=null;function m0(e){vh(e,0)}function tu(e){var t=Ca(e);if(gf(t))return e}function Hf(e,t){if(e==="change")return t}var Bf=!1;if(ln){var no;if(ln){var lo="oninput"in document;if(!lo){var kf=document.createElement("div");kf.setAttribute("oninput","return;"),lo=typeof kf.oninput=="function"}no=lo}else no=!1;Bf=no&&(!document.documentMode||9<document.documentMode)}function Gf(){Ua&&(Ua.detachEvent("onpropertychange",Yf),La=Ua=null)}function Yf(e){if(e.propertyName==="value"&&tu(La)){var t=[];jf(t,La,e,Ki(e)),Rf(m0,t)}}function h0(e,t,n){e==="focusin"?(Gf(),Ua=t,La=n,Ua.attachEvent("onpropertychange",Yf)):e==="focusout"&&Gf()}function p0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return tu(La)}function v0(e,t){if(e==="click")return tu(t)}function g0(e,t){if(e==="input"||e==="change")return tu(t)}function y0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var vt=typeof Object.is=="function"?Object.is:y0;function ja(e,t){if(vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var r=n[a];if(!qt.call(t,r)||!vt(e[r],t[r]))return!1}return!0}function qf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Vf(e,t){var n=qf(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=qf(n)}}function Xf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Xf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Qf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Pr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Pr(e.document)}return t}function ao(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var b0=ln&&"documentMode"in document&&11>=document.documentMode,Hl=null,ro=null,Ha=null,uo=!1;function Zf(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;uo||Hl==null||Hl!==Pr(a)||(a=Hl,"selectionStart"in a&&ao(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Ha&&ja(Ha,a)||(Ha=a,a=Gu(ro,"onSelect"),0<a.length&&(t=new Ir("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=Hl)))}function rl(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Bl={animationend:rl("Animation","AnimationEnd"),animationiteration:rl("Animation","AnimationIteration"),animationstart:rl("Animation","AnimationStart"),transitionrun:rl("Transition","TransitionRun"),transitionstart:rl("Transition","TransitionStart"),transitioncancel:rl("Transition","TransitionCancel"),transitionend:rl("Transition","TransitionEnd")},io={},Kf={};ln&&(Kf=document.createElement("div").style,"AnimationEvent"in window||(delete Bl.animationend.animation,delete Bl.animationiteration.animation,delete Bl.animationstart.animation),"TransitionEvent"in window||delete Bl.transitionend.transition);function ul(e){if(io[e])return io[e];if(!Bl[e])return e;var t=Bl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Kf)return io[e]=t[n];return e}var Pf=ul("animationend"),$f=ul("animationiteration"),Jf=ul("animationstart"),S0=ul("transitionrun"),x0=ul("transitionstart"),E0=ul("transitioncancel"),Ff=ul("transitionend"),Wf=new Map,oo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");oo.push("scrollEnd");function Lt(e,t){Wf.set(e,t),ll(t,[e])}var If=new WeakMap;function Tt(e,t){if(typeof e=="object"&&e!==null){var n=If.get(e);return n!==void 0?n:(t={value:e,source:t,stack:pf(t)},If.set(e,t),t)}return{value:e,source:t,stack:pf(t)}}var Ct=[],kl=0,co=0;function nu(){for(var e=kl,t=co=kl=0;t<e;){var n=Ct[t];Ct[t++]=null;var a=Ct[t];Ct[t++]=null;var r=Ct[t];Ct[t++]=null;var c=Ct[t];if(Ct[t++]=null,a!==null&&r!==null){var d=a.pending;d===null?r.next=r:(r.next=d.next,d.next=r),a.pending=r}c!==0&&ed(n,r,c)}}function lu(e,t,n,a){Ct[kl++]=e,Ct[kl++]=t,Ct[kl++]=n,Ct[kl++]=a,co|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function so(e,t,n,a){return lu(e,t,n,a),au(e)}function Gl(e,t){return lu(e,null,null,t),au(e)}function ed(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var r=!1,c=e.return;c!==null;)c.childLanes|=n,a=c.alternate,a!==null&&(a.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(r=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,r&&t!==null&&(r=31-pt(n),e=c.hiddenUpdates,a=e[r],a===null?e[r]=[t]:a.push(t),t.lane=n|536870912),c):null}function au(e){if(50<or)throw or=0,gc=null,Error(i(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Yl={};function w0(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function gt(e,t,n,a){return new w0(e,t,n,a)}function fo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function an(e,t){var n=e.alternate;return n===null?(n=gt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function td(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ru(e,t,n,a,r,c){var d=0;if(a=e,typeof e=="function")fo(e)&&(d=1);else if(typeof e=="string")d=Ab(e,n,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case le:return e=gt(31,n,t,r),e.elementType=le,e.lanes=c,e;case C:return il(n.children,r,c,t);case R:d=8,r|=24;break;case T:return e=gt(12,n,t,r|2),e.elementType=T,e.lanes=c,e;case Y:return e=gt(13,n,t,r),e.elementType=Y,e.lanes=c,e;case W:return e=gt(19,n,t,r),e.elementType=W,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case N:case k:d=10;break e;case H:d=9;break e;case X:d=11;break e;case $:d=14;break e;case Z:d=16,a=null;break e}d=29,n=Error(i(130,e===null?"null":typeof e,"")),a=null}return t=gt(d,n,t,r),t.elementType=e,t.type=a,t.lanes=c,t}function il(e,t,n,a){return e=gt(7,e,a,t),e.lanes=n,e}function mo(e,t,n){return e=gt(6,e,null,t),e.lanes=n,e}function ho(e,t,n){return t=gt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ql=[],Vl=0,uu=null,iu=0,Ot=[],_t=0,ol=null,rn=1,un="";function cl(e,t){ql[Vl++]=iu,ql[Vl++]=uu,uu=e,iu=t}function nd(e,t,n){Ot[_t++]=rn,Ot[_t++]=un,Ot[_t++]=ol,ol=e;var a=rn;e=un;var r=32-pt(a)-1;a&=~(1<<r),n+=1;var c=32-pt(t)+r;if(30<c){var d=r-r%5;c=(a&(1<<d)-1).toString(32),a>>=d,r-=d,rn=1<<32-pt(t)+r|n<<r|a,un=c+e}else rn=1<<c|n<<r|a,un=e}function po(e){e.return!==null&&(cl(e,1),nd(e,1,0))}function vo(e){for(;e===uu;)uu=ql[--Vl],ql[Vl]=null,iu=ql[--Vl],ql[Vl]=null;for(;e===ol;)ol=Ot[--_t],Ot[_t]=null,un=Ot[--_t],Ot[_t]=null,rn=Ot[--_t],Ot[_t]=null}var rt=null,Be=null,Te=!1,sl=null,Xt=!1,go=Error(i(519));function fl(e){var t=Error(i(418,""));throw Ga(Tt(t,e)),go}function ld(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[tt]=e,t[ot]=a,n){case"dialog":xe("cancel",t),xe("close",t);break;case"iframe":case"object":case"embed":xe("load",t);break;case"video":case"audio":for(n=0;n<sr.length;n++)xe(sr[n],t);break;case"source":xe("error",t);break;case"img":case"image":case"link":xe("error",t),xe("load",t);break;case"details":xe("toggle",t);break;case"input":xe("invalid",t),yf(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Kr(t);break;case"select":xe("invalid",t);break;case"textarea":xe("invalid",t),Sf(t,a.value,a.defaultValue,a.children),Kr(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||Sh(t.textContent,n)?(a.popover!=null&&(xe("beforetoggle",t),xe("toggle",t)),a.onScroll!=null&&xe("scroll",t),a.onScrollEnd!=null&&xe("scrollend",t),a.onClick!=null&&(t.onclick=Yu),t=!0):t=!1,t||fl(e)}function ad(e){for(rt=e.return;rt;)switch(rt.tag){case 5:case 13:Xt=!1;return;case 27:case 3:Xt=!0;return;default:rt=rt.return}}function Ba(e){if(e!==rt)return!1;if(!Te)return ad(e),Te=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||zc(e.type,e.memoizedProps)),n=!n),n&&Be&&fl(e),ad(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Be=Ht(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Be=null}}else t===27?(t=Be,Xn(e.type)?(e=Hc,Hc=null,Be=e):Be=t):Be=rt?Ht(e.stateNode.nextSibling):null;return!0}function ka(){Be=rt=null,Te=!1}function rd(){var e=sl;return e!==null&&(dt===null?dt=e:dt.push.apply(dt,e),sl=null),e}function Ga(e){sl===null?sl=[e]:sl.push(e)}var yo=V(null),dl=null,on=null;function On(e,t,n){P(yo,t._currentValue),t._currentValue=n}function cn(e){e._currentValue=yo.current,F(yo)}function bo(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function So(e,t,n,a){var r=e.child;for(r!==null&&(r.return=e);r!==null;){var c=r.dependencies;if(c!==null){var d=r.child;c=c.firstContext;e:for(;c!==null;){var b=c;c=r;for(var x=0;x<t.length;x++)if(b.context===t[x]){c.lanes|=n,b=c.alternate,b!==null&&(b.lanes|=n),bo(c.return,n,e),a||(d=null);break e}c=b.next}}else if(r.tag===18){if(d=r.return,d===null)throw Error(i(341));d.lanes|=n,c=d.alternate,c!==null&&(c.lanes|=n),bo(d,n,e),d=null}else d=r.child;if(d!==null)d.return=r;else for(d=r;d!==null;){if(d===e){d=null;break}if(r=d.sibling,r!==null){r.return=d.return,d=r;break}d=d.return}r=d}}function Ya(e,t,n,a){e=null;for(var r=t,c=!1;r!==null;){if(!c){if((r.flags&524288)!==0)c=!0;else if((r.flags&262144)!==0)break}if(r.tag===10){var d=r.alternate;if(d===null)throw Error(i(387));if(d=d.memoizedProps,d!==null){var b=r.type;vt(r.pendingProps.value,d.value)||(e!==null?e.push(b):e=[b])}}else if(r===ue.current){if(d=r.alternate,d===null)throw Error(i(387));d.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(e!==null?e.push(vr):e=[vr])}r=r.return}e!==null&&So(t,e,n,a),t.flags|=262144}function ou(e){for(e=e.firstContext;e!==null;){if(!vt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ml(e){dl=e,on=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function nt(e){return ud(dl,e)}function cu(e,t){return dl===null&&ml(e),ud(e,t)}function ud(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},on===null){if(e===null)throw Error(i(308));on=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else on=on.next=t;return n}var R0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},A0=l.unstable_scheduleCallback,M0=l.unstable_NormalPriority,Xe={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function xo(){return{controller:new R0,data:new Map,refCount:0}}function qa(e){e.refCount--,e.refCount===0&&A0(M0,function(){e.controller.abort()})}var Va=null,Eo=0,Xl=0,Ql=null;function T0(e,t){if(Va===null){var n=Va=[];Eo=0,Xl=Rc(),Ql={status:"pending",value:void 0,then:function(a){n.push(a)}}}return Eo++,t.then(id,id),t}function id(){if(--Eo===0&&Va!==null){Ql!==null&&(Ql.status="fulfilled");var e=Va;Va=null,Xl=0,Ql=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function C0(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(r){n.push(r)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var r=0;r<n.length;r++)(0,n[r])(t)},function(r){for(a.status="rejected",a.reason=r,r=0;r<n.length;r++)(0,n[r])(void 0)}),a}var od=z.S;z.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&T0(e,t),od!==null&&od(e,t)};var hl=V(null);function wo(){var e=hl.current;return e!==null?e:Ue.pooledCache}function su(e,t){t===null?P(hl,hl.current):P(hl,t.pool)}function cd(){var e=wo();return e===null?null:{parent:Xe._currentValue,pool:e}}var Xa=Error(i(460)),sd=Error(i(474)),fu=Error(i(542)),Ro={then:function(){}};function fd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function du(){}function dd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(du,du),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,hd(e),e;default:if(typeof t.status=="string")t.then(du,du);else{if(e=Ue,e!==null&&100<e.shellSuspendCounter)throw Error(i(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var r=t;r.status="fulfilled",r.value=a}},function(a){if(t.status==="pending"){var r=t;r.status="rejected",r.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,hd(e),e}throw Qa=t,Xa}}var Qa=null;function md(){if(Qa===null)throw Error(i(459));var e=Qa;return Qa=null,e}function hd(e){if(e===Xa||e===fu)throw Error(i(483))}var _n=!1;function Ao(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Mo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Dn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Nn(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(Ce&2)!==0){var r=a.pending;return r===null?t.next=t:(t.next=r.next,r.next=t),a.pending=t,t=au(e),ed(e,null,n),t}return lu(e,a,t,n),au(e)}function Za(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,uf(e,n)}}function To(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var r=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var d={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?r=c=d:c=c.next=d,n=n.next}while(n!==null);c===null?r=c=t:c=c.next=t}else r=c=t;n={baseState:a.baseState,firstBaseUpdate:r,lastBaseUpdate:c,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Co=!1;function Ka(){if(Co){var e=Ql;if(e!==null)throw e}}function Pa(e,t,n,a){Co=!1;var r=e.updateQueue;_n=!1;var c=r.firstBaseUpdate,d=r.lastBaseUpdate,b=r.shared.pending;if(b!==null){r.shared.pending=null;var x=b,U=x.next;x.next=null,d===null?c=U:d.next=U,d=x;var G=e.alternate;G!==null&&(G=G.updateQueue,b=G.lastBaseUpdate,b!==d&&(b===null?G.firstBaseUpdate=U:b.next=U,G.lastBaseUpdate=x))}if(c!==null){var Q=r.baseState;d=0,G=U=x=null,b=c;do{var L=b.lane&-536870913,j=L!==b.lane;if(j?(we&L)===L:(a&L)===L){L!==0&&L===Xl&&(Co=!0),G!==null&&(G=G.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});e:{var ce=e,ie=b;L=t;var Ne=n;switch(ie.tag){case 1:if(ce=ie.payload,typeof ce=="function"){Q=ce.call(Ne,Q,L);break e}Q=ce;break e;case 3:ce.flags=ce.flags&-65537|128;case 0:if(ce=ie.payload,L=typeof ce=="function"?ce.call(Ne,Q,L):ce,L==null)break e;Q=y({},Q,L);break e;case 2:_n=!0}}L=b.callback,L!==null&&(e.flags|=64,j&&(e.flags|=8192),j=r.callbacks,j===null?r.callbacks=[L]:j.push(L))}else j={lane:L,tag:b.tag,payload:b.payload,callback:b.callback,next:null},G===null?(U=G=j,x=Q):G=G.next=j,d|=L;if(b=b.next,b===null){if(b=r.shared.pending,b===null)break;j=b,b=j.next,j.next=null,r.lastBaseUpdate=j,r.shared.pending=null}}while(!0);G===null&&(x=Q),r.baseState=x,r.firstBaseUpdate=U,r.lastBaseUpdate=G,c===null&&(r.shared.lanes=0),Gn|=d,e.lanes=d,e.memoizedState=Q}}function pd(e,t){if(typeof e!="function")throw Error(i(191,e));e.call(t)}function vd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)pd(n[e],t)}var Zl=V(null),mu=V(0);function gd(e,t){e=vn,P(mu,e),P(Zl,t),vn=e|t.baseLanes}function Oo(){P(mu,vn),P(Zl,Zl.current)}function _o(){vn=mu.current,F(Zl),F(mu)}var zn=0,pe=null,_e=null,qe=null,hu=!1,Kl=!1,pl=!1,pu=0,$a=0,Pl=null,O0=0;function Ge(){throw Error(i(321))}function Do(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!vt(e[n],t[n]))return!1;return!0}function No(e,t,n,a,r,c){return zn=c,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=e===null||e.memoizedState===null?em:tm,pl=!1,c=n(a,r),pl=!1,Kl&&(c=bd(t,n,a,r)),yd(e),c}function yd(e){z.H=xu;var t=_e!==null&&_e.next!==null;if(zn=0,qe=_e=pe=null,hu=!1,$a=0,Pl=null,t)throw Error(i(300));e===null||Pe||(e=e.dependencies,e!==null&&ou(e)&&(Pe=!0))}function bd(e,t,n,a){pe=e;var r=0;do{if(Kl&&(Pl=null),$a=0,Kl=!1,25<=r)throw Error(i(301));if(r+=1,qe=_e=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}z.H=j0,c=t(n,a)}while(Kl);return c}function _0(){var e=z.H,t=e.useState()[0];return t=typeof t.then=="function"?Ja(t):t,e=e.useState()[0],(_e!==null?_e.memoizedState:null)!==e&&(pe.flags|=1024),t}function zo(){var e=pu!==0;return pu=0,e}function Uo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Lo(e){if(hu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}hu=!1}zn=0,qe=_e=pe=null,Kl=!1,$a=pu=0,Pl=null}function st(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return qe===null?pe.memoizedState=qe=e:qe=qe.next=e,qe}function Ve(){if(_e===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=_e.next;var t=qe===null?pe.memoizedState:qe.next;if(t!==null)qe=t,_e=e;else{if(e===null)throw pe.alternate===null?Error(i(467)):Error(i(310));_e=e,e={memoizedState:_e.memoizedState,baseState:_e.baseState,baseQueue:_e.baseQueue,queue:_e.queue,next:null},qe===null?pe.memoizedState=qe=e:qe=qe.next=e}return qe}function jo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ja(e){var t=$a;return $a+=1,Pl===null&&(Pl=[]),e=dd(Pl,e,t),t=pe,(qe===null?t.memoizedState:qe.next)===null&&(t=t.alternate,z.H=t===null||t.memoizedState===null?em:tm),e}function vu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ja(e);if(e.$$typeof===k)return nt(e)}throw Error(i(438,String(e)))}function Ho(e){var t=null,n=pe.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=pe.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(r){return r.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=jo(),pe.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=se;return t.index++,n}function sn(e,t){return typeof t=="function"?t(e):t}function gu(e){var t=Ve();return Bo(t,_e,e)}function Bo(e,t,n){var a=e.queue;if(a===null)throw Error(i(311));a.lastRenderedReducer=n;var r=e.baseQueue,c=a.pending;if(c!==null){if(r!==null){var d=r.next;r.next=c.next,c.next=d}t.baseQueue=r=c,a.pending=null}if(c=e.baseState,r===null)e.memoizedState=c;else{t=r.next;var b=d=null,x=null,U=t,G=!1;do{var Q=U.lane&-536870913;if(Q!==U.lane?(we&Q)===Q:(zn&Q)===Q){var L=U.revertLane;if(L===0)x!==null&&(x=x.next={lane:0,revertLane:0,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null}),Q===Xl&&(G=!0);else if((zn&L)===L){U=U.next,L===Xl&&(G=!0);continue}else Q={lane:0,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},x===null?(b=x=Q,d=c):x=x.next=Q,pe.lanes|=L,Gn|=L;Q=U.action,pl&&n(c,Q),c=U.hasEagerState?U.eagerState:n(c,Q)}else L={lane:Q,revertLane:U.revertLane,action:U.action,hasEagerState:U.hasEagerState,eagerState:U.eagerState,next:null},x===null?(b=x=L,d=c):x=x.next=L,pe.lanes|=Q,Gn|=Q;U=U.next}while(U!==null&&U!==t);if(x===null?d=c:x.next=b,!vt(c,e.memoizedState)&&(Pe=!0,G&&(n=Ql,n!==null)))throw n;e.memoizedState=c,e.baseState=d,e.baseQueue=x,a.lastRenderedState=c}return r===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function ko(e){var t=Ve(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var a=n.dispatch,r=n.pending,c=t.memoizedState;if(r!==null){n.pending=null;var d=r=r.next;do c=e(c,d.action),d=d.next;while(d!==r);vt(c,t.memoizedState)||(Pe=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,a]}function Sd(e,t,n){var a=pe,r=Ve(),c=Te;if(c){if(n===void 0)throw Error(i(407));n=n()}else n=t();var d=!vt((_e||r).memoizedState,n);d&&(r.memoizedState=n,Pe=!0),r=r.queue;var b=wd.bind(null,a,r,e);if(Fa(2048,8,b,[e]),r.getSnapshot!==t||d||qe!==null&&qe.memoizedState.tag&1){if(a.flags|=2048,$l(9,yu(),Ed.bind(null,a,r,n,t),null),Ue===null)throw Error(i(349));c||(zn&124)!==0||xd(a,t,n)}return n}function xd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t=jo(),pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ed(e,t,n,a){t.value=n,t.getSnapshot=a,Rd(t)&&Ad(e)}function wd(e,t,n){return n(function(){Rd(t)&&Ad(e)})}function Rd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!vt(e,n)}catch{return!0}}function Ad(e){var t=Gl(e,2);t!==null&&Et(t,e,2)}function Go(e){var t=st();if(typeof e=="function"){var n=e;if(e=n(),pl){Mn(!0);try{n()}finally{Mn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sn,lastRenderedState:e},t}function Md(e,t,n,a){return e.baseState=n,Bo(e,_e,typeof a=="function"?a:sn)}function D0(e,t,n,a,r){if(Su(e))throw Error(i(485));if(e=t.action,e!==null){var c={payload:r,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){c.listeners.push(d)}};z.T!==null?n(!0):c.isTransition=!1,a(c),n=t.pending,n===null?(c.next=t.pending=c,Td(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Td(e,t){var n=t.action,a=t.payload,r=e.state;if(t.isTransition){var c=z.T,d={};z.T=d;try{var b=n(r,a),x=z.S;x!==null&&x(d,b),Cd(e,t,b)}catch(U){Yo(e,t,U)}finally{z.T=c}}else try{c=n(r,a),Cd(e,t,c)}catch(U){Yo(e,t,U)}}function Cd(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){Od(e,t,a)},function(a){return Yo(e,t,a)}):Od(e,t,n)}function Od(e,t,n){t.status="fulfilled",t.value=n,_d(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Td(e,n)))}function Yo(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,_d(t),t=t.next;while(t!==a)}e.action=null}function _d(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Dd(e,t){return t}function Nd(e,t){if(Te){var n=Ue.formState;if(n!==null){e:{var a=pe;if(Te){if(Be){t:{for(var r=Be,c=Xt;r.nodeType!==8;){if(!c){r=null;break t}if(r=Ht(r.nextSibling),r===null){r=null;break t}}c=r.data,r=c==="F!"||c==="F"?r:null}if(r){Be=Ht(r.nextSibling),a=r.data==="F!";break e}}fl(a)}a=!1}a&&(t=n[0])}}return n=st(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dd,lastRenderedState:t},n.queue=a,n=Fd.bind(null,pe,a),a.dispatch=n,a=Go(!1),c=Zo.bind(null,pe,!1,a.queue),a=st(),r={state:t,dispatch:null,action:e,pending:null},a.queue=r,n=D0.bind(null,pe,r,c,n),r.dispatch=n,a.memoizedState=e,[t,n,!1]}function zd(e){var t=Ve();return Ud(t,_e,e)}function Ud(e,t,n){if(t=Bo(e,t,Dd)[0],e=gu(sn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Ja(t)}catch(d){throw d===Xa?fu:d}else a=t;t=Ve();var r=t.queue,c=r.dispatch;return n!==t.memoizedState&&(pe.flags|=2048,$l(9,yu(),N0.bind(null,r,n),null)),[a,c,e]}function N0(e,t){e.action=t}function Ld(e){var t=Ve(),n=_e;if(n!==null)return Ud(t,n,e);Ve(),t=t.memoizedState,n=Ve();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function $l(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=pe.updateQueue,t===null&&(t=jo(),pe.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function yu(){return{destroy:void 0,resource:void 0}}function jd(){return Ve().memoizedState}function bu(e,t,n,a){var r=st();a=a===void 0?null:a,pe.flags|=e,r.memoizedState=$l(1|t,yu(),n,a)}function Fa(e,t,n,a){var r=Ve();a=a===void 0?null:a;var c=r.memoizedState.inst;_e!==null&&a!==null&&Do(a,_e.memoizedState.deps)?r.memoizedState=$l(t,c,n,a):(pe.flags|=e,r.memoizedState=$l(1|t,c,n,a))}function Hd(e,t){bu(8390656,8,e,t)}function Bd(e,t){Fa(2048,8,e,t)}function kd(e,t){return Fa(4,2,e,t)}function Gd(e,t){return Fa(4,4,e,t)}function Yd(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function qd(e,t,n){n=n!=null?n.concat([e]):null,Fa(4,4,Yd.bind(null,t,e),n)}function qo(){}function Vd(e,t){var n=Ve();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&Do(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function Xd(e,t){var n=Ve();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&Do(t,a[1]))return a[0];if(a=e(),pl){Mn(!0);try{e()}finally{Mn(!1)}}return n.memoizedState=[a,t],a}function Vo(e,t,n){return n===void 0||(zn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Km(),pe.lanes|=e,Gn|=e,n)}function Qd(e,t,n,a){return vt(n,t)?n:Zl.current!==null?(e=Vo(e,n,a),vt(e,t)||(Pe=!0),e):(zn&42)===0?(Pe=!0,e.memoizedState=n):(e=Km(),pe.lanes|=e,Gn|=e,t)}function Zd(e,t,n,a,r){var c=K.p;K.p=c!==0&&8>c?c:8;var d=z.T,b={};z.T=b,Zo(e,!1,t,n);try{var x=r(),U=z.S;if(U!==null&&U(b,x),x!==null&&typeof x=="object"&&typeof x.then=="function"){var G=C0(x,a);Wa(e,t,G,xt(e))}else Wa(e,t,a,xt(e))}catch(Q){Wa(e,t,{then:function(){},status:"rejected",reason:Q},xt())}finally{K.p=c,z.T=d}}function z0(){}function Xo(e,t,n,a){if(e.tag!==5)throw Error(i(476));var r=Kd(e).queue;Zd(e,r,t,B,n===null?z0:function(){return Pd(e),n(a)})}function Kd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:B,baseState:B,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sn,lastRenderedState:B},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Pd(e){var t=Kd(e).next.queue;Wa(e,t,{},xt())}function Qo(){return nt(vr)}function $d(){return Ve().memoizedState}function Jd(){return Ve().memoizedState}function U0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=xt();e=Dn(n);var a=Nn(t,e,n);a!==null&&(Et(a,t,n),Za(a,t,n)),t={cache:xo()},e.payload=t;return}t=t.return}}function L0(e,t,n){var a=xt();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Su(e)?Wd(t,n):(n=so(e,t,n,a),n!==null&&(Et(n,e,a),Id(n,t,a)))}function Fd(e,t,n){var a=xt();Wa(e,t,n,a)}function Wa(e,t,n,a){var r={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Su(e))Wd(t,r);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var d=t.lastRenderedState,b=c(d,n);if(r.hasEagerState=!0,r.eagerState=b,vt(b,d))return lu(e,t,r,0),Ue===null&&nu(),!1}catch{}finally{}if(n=so(e,t,r,a),n!==null)return Et(n,e,a),Id(n,t,a),!0}return!1}function Zo(e,t,n,a){if(a={lane:2,revertLane:Rc(),action:a,hasEagerState:!1,eagerState:null,next:null},Su(e)){if(t)throw Error(i(479))}else t=so(e,n,a,2),t!==null&&Et(t,e,2)}function Su(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function Wd(e,t){Kl=hu=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Id(e,t,n){if((n&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,uf(e,n)}}var xu={readContext:nt,use:vu,useCallback:Ge,useContext:Ge,useEffect:Ge,useImperativeHandle:Ge,useLayoutEffect:Ge,useInsertionEffect:Ge,useMemo:Ge,useReducer:Ge,useRef:Ge,useState:Ge,useDebugValue:Ge,useDeferredValue:Ge,useTransition:Ge,useSyncExternalStore:Ge,useId:Ge,useHostTransitionStatus:Ge,useFormState:Ge,useActionState:Ge,useOptimistic:Ge,useMemoCache:Ge,useCacheRefresh:Ge},em={readContext:nt,use:vu,useCallback:function(e,t){return st().memoizedState=[e,t===void 0?null:t],e},useContext:nt,useEffect:Hd,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,bu(4194308,4,Yd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return bu(4194308,4,e,t)},useInsertionEffect:function(e,t){bu(4,2,e,t)},useMemo:function(e,t){var n=st();t=t===void 0?null:t;var a=e();if(pl){Mn(!0);try{e()}finally{Mn(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=st();if(n!==void 0){var r=n(t);if(pl){Mn(!0);try{n(t)}finally{Mn(!1)}}}else r=t;return a.memoizedState=a.baseState=r,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:r},a.queue=e,e=e.dispatch=L0.bind(null,pe,e),[a.memoizedState,e]},useRef:function(e){var t=st();return e={current:e},t.memoizedState=e},useState:function(e){e=Go(e);var t=e.queue,n=Fd.bind(null,pe,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:qo,useDeferredValue:function(e,t){var n=st();return Vo(n,e,t)},useTransition:function(){var e=Go(!1);return e=Zd.bind(null,pe,e.queue,!0,!1),st().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=pe,r=st();if(Te){if(n===void 0)throw Error(i(407));n=n()}else{if(n=t(),Ue===null)throw Error(i(349));(we&124)!==0||xd(a,t,n)}r.memoizedState=n;var c={value:n,getSnapshot:t};return r.queue=c,Hd(wd.bind(null,a,c,e),[e]),a.flags|=2048,$l(9,yu(),Ed.bind(null,a,c,n,t),null),n},useId:function(){var e=st(),t=Ue.identifierPrefix;if(Te){var n=un,a=rn;n=(a&~(1<<32-pt(a)-1)).toString(32)+n,t="«"+t+"R"+n,n=pu++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=O0++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Qo,useFormState:Nd,useActionState:Nd,useOptimistic:function(e){var t=st();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Zo.bind(null,pe,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ho,useCacheRefresh:function(){return st().memoizedState=U0.bind(null,pe)}},tm={readContext:nt,use:vu,useCallback:Vd,useContext:nt,useEffect:Bd,useImperativeHandle:qd,useInsertionEffect:kd,useLayoutEffect:Gd,useMemo:Xd,useReducer:gu,useRef:jd,useState:function(){return gu(sn)},useDebugValue:qo,useDeferredValue:function(e,t){var n=Ve();return Qd(n,_e.memoizedState,e,t)},useTransition:function(){var e=gu(sn)[0],t=Ve().memoizedState;return[typeof e=="boolean"?e:Ja(e),t]},useSyncExternalStore:Sd,useId:$d,useHostTransitionStatus:Qo,useFormState:zd,useActionState:zd,useOptimistic:function(e,t){var n=Ve();return Md(n,_e,e,t)},useMemoCache:Ho,useCacheRefresh:Jd},j0={readContext:nt,use:vu,useCallback:Vd,useContext:nt,useEffect:Bd,useImperativeHandle:qd,useInsertionEffect:kd,useLayoutEffect:Gd,useMemo:Xd,useReducer:ko,useRef:jd,useState:function(){return ko(sn)},useDebugValue:qo,useDeferredValue:function(e,t){var n=Ve();return _e===null?Vo(n,e,t):Qd(n,_e.memoizedState,e,t)},useTransition:function(){var e=ko(sn)[0],t=Ve().memoizedState;return[typeof e=="boolean"?e:Ja(e),t]},useSyncExternalStore:Sd,useId:$d,useHostTransitionStatus:Qo,useFormState:Ld,useActionState:Ld,useOptimistic:function(e,t){var n=Ve();return _e!==null?Md(n,_e,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ho,useCacheRefresh:Jd},Jl=null,Ia=0;function Eu(e){var t=Ia;return Ia+=1,Jl===null&&(Jl=[]),dd(Jl,e,t)}function er(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function wu(e,t){throw t.$$typeof===S?Error(i(525)):(e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function nm(e){var t=e._init;return t(e._payload)}function lm(e){function t(O,M){if(e){var D=O.deletions;D===null?(O.deletions=[M],O.flags|=16):D.push(M)}}function n(O,M){if(!e)return null;for(;M!==null;)t(O,M),M=M.sibling;return null}function a(O){for(var M=new Map;O!==null;)O.key!==null?M.set(O.key,O):M.set(O.index,O),O=O.sibling;return M}function r(O,M){return O=an(O,M),O.index=0,O.sibling=null,O}function c(O,M,D){return O.index=D,e?(D=O.alternate,D!==null?(D=D.index,D<M?(O.flags|=67108866,M):D):(O.flags|=67108866,M)):(O.flags|=1048576,M)}function d(O){return e&&O.alternate===null&&(O.flags|=67108866),O}function b(O,M,D,q){return M===null||M.tag!==6?(M=mo(D,O.mode,q),M.return=O,M):(M=r(M,D),M.return=O,M)}function x(O,M,D,q){var ee=D.type;return ee===C?G(O,M,D.props.children,q,D.key):M!==null&&(M.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===Z&&nm(ee)===M.type)?(M=r(M,D.props),er(M,D),M.return=O,M):(M=ru(D.type,D.key,D.props,null,O.mode,q),er(M,D),M.return=O,M)}function U(O,M,D,q){return M===null||M.tag!==4||M.stateNode.containerInfo!==D.containerInfo||M.stateNode.implementation!==D.implementation?(M=ho(D,O.mode,q),M.return=O,M):(M=r(M,D.children||[]),M.return=O,M)}function G(O,M,D,q,ee){return M===null||M.tag!==7?(M=il(D,O.mode,q,ee),M.return=O,M):(M=r(M,D),M.return=O,M)}function Q(O,M,D){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return M=mo(""+M,O.mode,D),M.return=O,M;if(typeof M=="object"&&M!==null){switch(M.$$typeof){case E:return D=ru(M.type,M.key,M.props,null,O.mode,D),er(D,M),D.return=O,D;case A:return M=ho(M,O.mode,D),M.return=O,M;case Z:var q=M._init;return M=q(M._payload),Q(O,M,D)}if(me(M)||fe(M))return M=il(M,O.mode,D,null),M.return=O,M;if(typeof M.then=="function")return Q(O,Eu(M),D);if(M.$$typeof===k)return Q(O,cu(O,M),D);wu(O,M)}return null}function L(O,M,D,q){var ee=M!==null?M.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return ee!==null?null:b(O,M,""+D,q);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case E:return D.key===ee?x(O,M,D,q):null;case A:return D.key===ee?U(O,M,D,q):null;case Z:return ee=D._init,D=ee(D._payload),L(O,M,D,q)}if(me(D)||fe(D))return ee!==null?null:G(O,M,D,q,null);if(typeof D.then=="function")return L(O,M,Eu(D),q);if(D.$$typeof===k)return L(O,M,cu(O,D),q);wu(O,D)}return null}function j(O,M,D,q,ee){if(typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint")return O=O.get(D)||null,b(M,O,""+q,ee);if(typeof q=="object"&&q!==null){switch(q.$$typeof){case E:return O=O.get(q.key===null?D:q.key)||null,x(M,O,q,ee);case A:return O=O.get(q.key===null?D:q.key)||null,U(M,O,q,ee);case Z:var ye=q._init;return q=ye(q._payload),j(O,M,D,q,ee)}if(me(q)||fe(q))return O=O.get(D)||null,G(M,O,q,ee,null);if(typeof q.then=="function")return j(O,M,D,Eu(q),ee);if(q.$$typeof===k)return j(O,M,D,cu(M,q),ee);wu(M,q)}return null}function ce(O,M,D,q){for(var ee=null,ye=null,ae=M,oe=M=0,Je=null;ae!==null&&oe<D.length;oe++){ae.index>oe?(Je=ae,ae=null):Je=ae.sibling;var Me=L(O,ae,D[oe],q);if(Me===null){ae===null&&(ae=Je);break}e&&ae&&Me.alternate===null&&t(O,ae),M=c(Me,M,oe),ye===null?ee=Me:ye.sibling=Me,ye=Me,ae=Je}if(oe===D.length)return n(O,ae),Te&&cl(O,oe),ee;if(ae===null){for(;oe<D.length;oe++)ae=Q(O,D[oe],q),ae!==null&&(M=c(ae,M,oe),ye===null?ee=ae:ye.sibling=ae,ye=ae);return Te&&cl(O,oe),ee}for(ae=a(ae);oe<D.length;oe++)Je=j(ae,O,oe,D[oe],q),Je!==null&&(e&&Je.alternate!==null&&ae.delete(Je.key===null?oe:Je.key),M=c(Je,M,oe),ye===null?ee=Je:ye.sibling=Je,ye=Je);return e&&ae.forEach(function($n){return t(O,$n)}),Te&&cl(O,oe),ee}function ie(O,M,D,q){if(D==null)throw Error(i(151));for(var ee=null,ye=null,ae=M,oe=M=0,Je=null,Me=D.next();ae!==null&&!Me.done;oe++,Me=D.next()){ae.index>oe?(Je=ae,ae=null):Je=ae.sibling;var $n=L(O,ae,Me.value,q);if($n===null){ae===null&&(ae=Je);break}e&&ae&&$n.alternate===null&&t(O,ae),M=c($n,M,oe),ye===null?ee=$n:ye.sibling=$n,ye=$n,ae=Je}if(Me.done)return n(O,ae),Te&&cl(O,oe),ee;if(ae===null){for(;!Me.done;oe++,Me=D.next())Me=Q(O,Me.value,q),Me!==null&&(M=c(Me,M,oe),ye===null?ee=Me:ye.sibling=Me,ye=Me);return Te&&cl(O,oe),ee}for(ae=a(ae);!Me.done;oe++,Me=D.next())Me=j(ae,O,oe,Me.value,q),Me!==null&&(e&&Me.alternate!==null&&ae.delete(Me.key===null?oe:Me.key),M=c(Me,M,oe),ye===null?ee=Me:ye.sibling=Me,ye=Me);return e&&ae.forEach(function(Hb){return t(O,Hb)}),Te&&cl(O,oe),ee}function Ne(O,M,D,q){if(typeof D=="object"&&D!==null&&D.type===C&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case E:e:{for(var ee=D.key;M!==null;){if(M.key===ee){if(ee=D.type,ee===C){if(M.tag===7){n(O,M.sibling),q=r(M,D.props.children),q.return=O,O=q;break e}}else if(M.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===Z&&nm(ee)===M.type){n(O,M.sibling),q=r(M,D.props),er(q,D),q.return=O,O=q;break e}n(O,M);break}else t(O,M);M=M.sibling}D.type===C?(q=il(D.props.children,O.mode,q,D.key),q.return=O,O=q):(q=ru(D.type,D.key,D.props,null,O.mode,q),er(q,D),q.return=O,O=q)}return d(O);case A:e:{for(ee=D.key;M!==null;){if(M.key===ee)if(M.tag===4&&M.stateNode.containerInfo===D.containerInfo&&M.stateNode.implementation===D.implementation){n(O,M.sibling),q=r(M,D.children||[]),q.return=O,O=q;break e}else{n(O,M);break}else t(O,M);M=M.sibling}q=ho(D,O.mode,q),q.return=O,O=q}return d(O);case Z:return ee=D._init,D=ee(D._payload),Ne(O,M,D,q)}if(me(D))return ce(O,M,D,q);if(fe(D)){if(ee=fe(D),typeof ee!="function")throw Error(i(150));return D=ee.call(D),ie(O,M,D,q)}if(typeof D.then=="function")return Ne(O,M,Eu(D),q);if(D.$$typeof===k)return Ne(O,M,cu(O,D),q);wu(O,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,M!==null&&M.tag===6?(n(O,M.sibling),q=r(M,D),q.return=O,O=q):(n(O,M),q=mo(D,O.mode,q),q.return=O,O=q),d(O)):n(O,M)}return function(O,M,D,q){try{Ia=0;var ee=Ne(O,M,D,q);return Jl=null,ee}catch(ae){if(ae===Xa||ae===fu)throw ae;var ye=gt(29,ae,null,O.mode);return ye.lanes=q,ye.return=O,ye}finally{}}}var Fl=lm(!0),am=lm(!1),Dt=V(null),Qt=null;function Un(e){var t=e.alternate;P(Qe,Qe.current&1),P(Dt,e),Qt===null&&(t===null||Zl.current!==null||t.memoizedState!==null)&&(Qt=e)}function rm(e){if(e.tag===22){if(P(Qe,Qe.current),P(Dt,e),Qt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Qt=e)}}else Ln()}function Ln(){P(Qe,Qe.current),P(Dt,Dt.current)}function fn(e){F(Dt),Qt===e&&(Qt=null),F(Qe)}var Qe=V(0);function Ru(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||jc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Ko(e,t,n,a){t=e.memoizedState,n=n(a,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Po={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=xt(),r=Dn(a);r.payload=t,n!=null&&(r.callback=n),t=Nn(e,r,a),t!==null&&(Et(t,e,a),Za(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=xt(),r=Dn(a);r.tag=1,r.payload=t,n!=null&&(r.callback=n),t=Nn(e,r,a),t!==null&&(Et(t,e,a),Za(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xt(),a=Dn(n);a.tag=2,t!=null&&(a.callback=t),t=Nn(e,a,n),t!==null&&(Et(t,e,n),Za(t,e,n))}};function um(e,t,n,a,r,c,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,c,d):t.prototype&&t.prototype.isPureReactComponent?!ja(n,a)||!ja(r,c):!0}function im(e,t,n,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&Po.enqueueReplaceState(t,t.state,null)}function vl(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var r in e)n[r]===void 0&&(n[r]=e[r])}return n}var Au=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function om(e){Au(e)}function cm(e){console.error(e)}function sm(e){Au(e)}function Mu(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function fm(e,t,n){try{var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function $o(e,t,n){return n=Dn(n),n.tag=3,n.payload={element:null},n.callback=function(){Mu(e,t)},n}function dm(e){return e=Dn(e),e.tag=3,e}function mm(e,t,n,a){var r=n.type.getDerivedStateFromError;if(typeof r=="function"){var c=a.value;e.payload=function(){return r(c)},e.callback=function(){fm(t,n,a)}}var d=n.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){fm(t,n,a),typeof r!="function"&&(Yn===null?Yn=new Set([this]):Yn.add(this));var b=a.stack;this.componentDidCatch(a.value,{componentStack:b!==null?b:""})})}function H0(e,t,n,a,r){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&Ya(t,n,r,!0),n=Dt.current,n!==null){switch(n.tag){case 13:return Qt===null?bc():n.alternate===null&&ke===0&&(ke=3),n.flags&=-257,n.flags|=65536,n.lanes=r,a===Ro?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),xc(e,a,r)),!1;case 22:return n.flags|=65536,a===Ro?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),xc(e,a,r)),!1}throw Error(i(435,n.tag))}return xc(e,a,r),bc(),!1}if(Te)return t=Dt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=r,a!==go&&(e=Error(i(422),{cause:a}),Ga(Tt(e,n)))):(a!==go&&(t=Error(i(423),{cause:a}),Ga(Tt(t,n))),e=e.current.alternate,e.flags|=65536,r&=-r,e.lanes|=r,a=Tt(a,n),r=$o(e.stateNode,a,r),To(e,r),ke!==4&&(ke=2)),!1;var c=Error(i(520),{cause:a});if(c=Tt(c,n),ir===null?ir=[c]:ir.push(c),ke!==4&&(ke=2),t===null)return!0;a=Tt(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=r&-r,n.lanes|=e,e=$o(n.stateNode,a,e),To(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Yn===null||!Yn.has(c))))return n.flags|=65536,r&=-r,n.lanes|=r,r=dm(r),mm(r,e,n,a),To(n,r),!1}n=n.return}while(n!==null);return!1}var hm=Error(i(461)),Pe=!1;function Fe(e,t,n,a){t.child=e===null?am(t,null,n,a):Fl(t,e.child,n,a)}function pm(e,t,n,a,r){n=n.render;var c=t.ref;if("ref"in a){var d={};for(var b in a)b!=="ref"&&(d[b]=a[b])}else d=a;return ml(t),a=No(e,t,n,d,c,r),b=zo(),e!==null&&!Pe?(Uo(e,t,r),dn(e,t,r)):(Te&&b&&po(t),t.flags|=1,Fe(e,t,a,r),t.child)}function vm(e,t,n,a,r){if(e===null){var c=n.type;return typeof c=="function"&&!fo(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,gm(e,t,c,a,r)):(e=ru(n.type,null,a,t,t.mode,r),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!lc(e,r)){var d=c.memoizedProps;if(n=n.compare,n=n!==null?n:ja,n(d,a)&&e.ref===t.ref)return dn(e,t,r)}return t.flags|=1,e=an(c,a),e.ref=t.ref,e.return=t,t.child=e}function gm(e,t,n,a,r){if(e!==null){var c=e.memoizedProps;if(ja(c,a)&&e.ref===t.ref)if(Pe=!1,t.pendingProps=a=c,lc(e,r))(e.flags&131072)!==0&&(Pe=!0);else return t.lanes=e.lanes,dn(e,t,r)}return Jo(e,t,n,a,r)}function ym(e,t,n){var a=t.pendingProps,r=a.children,c=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=c!==null?c.baseLanes|n:n,e!==null){for(r=t.child=e.child,c=0;r!==null;)c=c|r.lanes|r.childLanes,r=r.sibling;t.childLanes=c&~a}else t.childLanes=0,t.child=null;return bm(e,t,a,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&su(t,c!==null?c.cachePool:null),c!==null?gd(t,c):Oo(),rm(t);else return t.lanes=t.childLanes=536870912,bm(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(su(t,c.cachePool),gd(t,c),Ln(),t.memoizedState=null):(e!==null&&su(t,null),Oo(),Ln());return Fe(e,t,r,n),t.child}function bm(e,t,n,a){var r=wo();return r=r===null?null:{parent:Xe._currentValue,pool:r},t.memoizedState={baseLanes:n,cachePool:r},e!==null&&su(t,null),Oo(),rm(t),e!==null&&Ya(e,t,a,!0),null}function Tu(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(i(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Jo(e,t,n,a,r){return ml(t),n=No(e,t,n,a,void 0,r),a=zo(),e!==null&&!Pe?(Uo(e,t,r),dn(e,t,r)):(Te&&a&&po(t),t.flags|=1,Fe(e,t,n,r),t.child)}function Sm(e,t,n,a,r,c){return ml(t),t.updateQueue=null,n=bd(t,a,n,r),yd(e),a=zo(),e!==null&&!Pe?(Uo(e,t,c),dn(e,t,c)):(Te&&a&&po(t),t.flags|=1,Fe(e,t,n,c),t.child)}function xm(e,t,n,a,r){if(ml(t),t.stateNode===null){var c=Yl,d=n.contextType;typeof d=="object"&&d!==null&&(c=nt(d)),c=new n(a,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=Po,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=a,c.state=t.memoizedState,c.refs={},Ao(t),d=n.contextType,c.context=typeof d=="object"&&d!==null?nt(d):Yl,c.state=t.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(Ko(t,n,d,a),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(d=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),d!==c.state&&Po.enqueueReplaceState(c,c.state,null),Pa(t,a,c,r),Ka(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){c=t.stateNode;var b=t.memoizedProps,x=vl(n,b);c.props=x;var U=c.context,G=n.contextType;d=Yl,typeof G=="object"&&G!==null&&(d=nt(G));var Q=n.getDerivedStateFromProps;G=typeof Q=="function"||typeof c.getSnapshotBeforeUpdate=="function",b=t.pendingProps!==b,G||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(b||U!==d)&&im(t,c,a,d),_n=!1;var L=t.memoizedState;c.state=L,Pa(t,a,c,r),Ka(),U=t.memoizedState,b||L!==U||_n?(typeof Q=="function"&&(Ko(t,n,Q,a),U=t.memoizedState),(x=_n||um(t,n,x,a,L,U,d))?(G||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=U),c.props=a,c.state=U,c.context=d,a=x):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{c=t.stateNode,Mo(e,t),d=t.memoizedProps,G=vl(n,d),c.props=G,Q=t.pendingProps,L=c.context,U=n.contextType,x=Yl,typeof U=="object"&&U!==null&&(x=nt(U)),b=n.getDerivedStateFromProps,(U=typeof b=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(d!==Q||L!==x)&&im(t,c,a,x),_n=!1,L=t.memoizedState,c.state=L,Pa(t,a,c,r),Ka();var j=t.memoizedState;d!==Q||L!==j||_n||e!==null&&e.dependencies!==null&&ou(e.dependencies)?(typeof b=="function"&&(Ko(t,n,b,a),j=t.memoizedState),(G=_n||um(t,n,G,a,L,j,x)||e!==null&&e.dependencies!==null&&ou(e.dependencies))?(U||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(a,j,x),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(a,j,x)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&L===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&L===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=j),c.props=a,c.state=j,c.context=x,a=G):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&L===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&L===e.memoizedState||(t.flags|=1024),a=!1)}return c=a,Tu(e,t),a=(t.flags&128)!==0,c||a?(c=t.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&a?(t.child=Fl(t,e.child,null,r),t.child=Fl(t,null,n,r)):Fe(e,t,n,r),t.memoizedState=c.state,e=t.child):e=dn(e,t,r),e}function Em(e,t,n,a){return ka(),t.flags|=256,Fe(e,t,n,a),t.child}var Fo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Wo(e){return{baseLanes:e,cachePool:cd()}}function Io(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Nt),e}function wm(e,t,n){var a=t.pendingProps,r=!1,c=(t.flags&128)!==0,d;if((d=c)||(d=e!==null&&e.memoizedState===null?!1:(Qe.current&2)!==0),d&&(r=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(Te){if(r?Un(t):Ln(),Te){var b=Be,x;if(x=b){e:{for(x=b,b=Xt;x.nodeType!==8;){if(!b){b=null;break e}if(x=Ht(x.nextSibling),x===null){b=null;break e}}b=x}b!==null?(t.memoizedState={dehydrated:b,treeContext:ol!==null?{id:rn,overflow:un}:null,retryLane:536870912,hydrationErrors:null},x=gt(18,null,null,0),x.stateNode=b,x.return=t,t.child=x,rt=t,Be=null,x=!0):x=!1}x||fl(t)}if(b=t.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return jc(b)?t.lanes=32:t.lanes=536870912,null;fn(t)}return b=a.children,a=a.fallback,r?(Ln(),r=t.mode,b=Cu({mode:"hidden",children:b},r),a=il(a,r,n,null),b.return=t,a.return=t,b.sibling=a,t.child=b,r=t.child,r.memoizedState=Wo(n),r.childLanes=Io(e,d,n),t.memoizedState=Fo,a):(Un(t),ec(t,b))}if(x=e.memoizedState,x!==null&&(b=x.dehydrated,b!==null)){if(c)t.flags&256?(Un(t),t.flags&=-257,t=tc(e,t,n)):t.memoizedState!==null?(Ln(),t.child=e.child,t.flags|=128,t=null):(Ln(),r=a.fallback,b=t.mode,a=Cu({mode:"visible",children:a.children},b),r=il(r,b,n,null),r.flags|=2,a.return=t,r.return=t,a.sibling=r,t.child=a,Fl(t,e.child,null,n),a=t.child,a.memoizedState=Wo(n),a.childLanes=Io(e,d,n),t.memoizedState=Fo,t=r);else if(Un(t),jc(b)){if(d=b.nextSibling&&b.nextSibling.dataset,d)var U=d.dgst;d=U,a=Error(i(419)),a.stack="",a.digest=d,Ga({value:a,source:null,stack:null}),t=tc(e,t,n)}else if(Pe||Ya(e,t,n,!1),d=(n&e.childLanes)!==0,Pe||d){if(d=Ue,d!==null&&(a=n&-n,a=(a&42)!==0?1:ji(a),a=(a&(d.suspendedLanes|n))!==0?0:a,a!==0&&a!==x.retryLane))throw x.retryLane=a,Gl(e,a),Et(d,e,a),hm;b.data==="$?"||bc(),t=tc(e,t,n)}else b.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=x.treeContext,Be=Ht(b.nextSibling),rt=t,Te=!0,sl=null,Xt=!1,e!==null&&(Ot[_t++]=rn,Ot[_t++]=un,Ot[_t++]=ol,rn=e.id,un=e.overflow,ol=t),t=ec(t,a.children),t.flags|=4096);return t}return r?(Ln(),r=a.fallback,b=t.mode,x=e.child,U=x.sibling,a=an(x,{mode:"hidden",children:a.children}),a.subtreeFlags=x.subtreeFlags&65011712,U!==null?r=an(U,r):(r=il(r,b,n,null),r.flags|=2),r.return=t,a.return=t,a.sibling=r,t.child=a,a=r,r=t.child,b=e.child.memoizedState,b===null?b=Wo(n):(x=b.cachePool,x!==null?(U=Xe._currentValue,x=x.parent!==U?{parent:U,pool:U}:x):x=cd(),b={baseLanes:b.baseLanes|n,cachePool:x}),r.memoizedState=b,r.childLanes=Io(e,d,n),t.memoizedState=Fo,a):(Un(t),n=e.child,e=n.sibling,n=an(n,{mode:"visible",children:a.children}),n.return=t,n.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=n,t.memoizedState=null,n)}function ec(e,t){return t=Cu({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Cu(e,t){return e=gt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function tc(e,t,n){return Fl(t,e.child,null,n),e=ec(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Rm(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),bo(e.return,t,n)}function nc(e,t,n,a,r){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=a,c.tail=n,c.tailMode=r)}function Am(e,t,n){var a=t.pendingProps,r=a.revealOrder,c=a.tail;if(Fe(e,t,a.children,n),a=Qe.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Rm(e,n,t);else if(e.tag===19)Rm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(P(Qe,a),r){case"forwards":for(n=t.child,r=null;n!==null;)e=n.alternate,e!==null&&Ru(e)===null&&(r=n),n=n.sibling;n=r,n===null?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),nc(t,!1,r,n,c);break;case"backwards":for(n=null,r=t.child,t.child=null;r!==null;){if(e=r.alternate,e!==null&&Ru(e)===null){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}nc(t,!0,n,null,c);break;case"together":nc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function dn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Gn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Ya(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,n=an(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=an(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function lc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ou(e)))}function B0(e,t,n){switch(t.tag){case 3:Ae(t,t.stateNode.containerInfo),On(t,Xe,e.memoizedState.cache),ka();break;case 27:case 5:Gt(t);break;case 4:Ae(t,t.stateNode.containerInfo);break;case 10:On(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(Un(t),t.flags|=128,null):(n&t.child.childLanes)!==0?wm(e,t,n):(Un(t),e=dn(e,t,n),e!==null?e.sibling:null);Un(t);break;case 19:var r=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(Ya(e,t,n,!1),a=(n&t.childLanes)!==0),r){if(a)return Am(e,t,n);t.flags|=128}if(r=t.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),P(Qe,Qe.current),a)break;return null;case 22:case 23:return t.lanes=0,ym(e,t,n);case 24:On(t,Xe,e.memoizedState.cache)}return dn(e,t,n)}function Mm(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Pe=!0;else{if(!lc(e,n)&&(t.flags&128)===0)return Pe=!1,B0(e,t,n);Pe=(e.flags&131072)!==0}else Pe=!1,Te&&(t.flags&1048576)!==0&&nd(t,iu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,r=a._init;if(a=r(a._payload),t.type=a,typeof a=="function")fo(a)?(e=vl(a,e),t.tag=1,t=xm(null,t,a,e,n)):(t.tag=0,t=Jo(null,t,a,e,n));else{if(a!=null){if(r=a.$$typeof,r===X){t.tag=11,t=pm(null,t,a,e,n);break e}else if(r===$){t.tag=14,t=vm(null,t,a,e,n);break e}}throw t=ge(a)||a,Error(i(306,t,""))}}return t;case 0:return Jo(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,r=vl(a,t.pendingProps),xm(e,t,a,r,n);case 3:e:{if(Ae(t,t.stateNode.containerInfo),e===null)throw Error(i(387));a=t.pendingProps;var c=t.memoizedState;r=c.element,Mo(e,t),Pa(t,a,null,n);var d=t.memoizedState;if(a=d.cache,On(t,Xe,a),a!==c.cache&&So(t,[Xe],n,!0),Ka(),a=d.element,c.isDehydrated)if(c={element:a,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Em(e,t,a,n);break e}else if(a!==r){r=Tt(Error(i(424)),t),Ga(r),t=Em(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Be=Ht(e.firstChild),rt=t,Te=!0,sl=null,Xt=!0,n=am(t,null,a,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ka(),a===r){t=dn(e,t,n);break e}Fe(e,t,a,n)}t=t.child}return t;case 26:return Tu(e,t),e===null?(n=_h(t.type,null,t.pendingProps,null))?t.memoizedState=n:Te||(n=t.type,e=t.pendingProps,a=qu(re.current).createElement(n),a[tt]=t,a[ot]=e,Ie(a,n,e),Ke(a),t.stateNode=a):t.memoizedState=_h(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Gt(t),e===null&&Te&&(a=t.stateNode=Th(t.type,t.pendingProps,re.current),rt=t,Xt=!0,r=Be,Xn(t.type)?(Hc=r,Be=Ht(a.firstChild)):Be=r),Fe(e,t,t.pendingProps.children,n),Tu(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Te&&((r=a=Be)&&(a=db(a,t.type,t.pendingProps,Xt),a!==null?(t.stateNode=a,rt=t,Be=Ht(a.firstChild),Xt=!1,r=!0):r=!1),r||fl(t)),Gt(t),r=t.type,c=t.pendingProps,d=e!==null?e.memoizedProps:null,a=c.children,zc(r,c)?a=null:d!==null&&zc(r,d)&&(t.flags|=32),t.memoizedState!==null&&(r=No(e,t,_0,null,null,n),vr._currentValue=r),Tu(e,t),Fe(e,t,a,n),t.child;case 6:return e===null&&Te&&((e=n=Be)&&(n=mb(n,t.pendingProps,Xt),n!==null?(t.stateNode=n,rt=t,Be=null,e=!0):e=!1),e||fl(t)),null;case 13:return wm(e,t,n);case 4:return Ae(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Fl(t,null,a,n):Fe(e,t,a,n),t.child;case 11:return pm(e,t,t.type,t.pendingProps,n);case 7:return Fe(e,t,t.pendingProps,n),t.child;case 8:return Fe(e,t,t.pendingProps.children,n),t.child;case 12:return Fe(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,On(t,t.type,a.value),Fe(e,t,a.children,n),t.child;case 9:return r=t.type._context,a=t.pendingProps.children,ml(t),r=nt(r),a=a(r),t.flags|=1,Fe(e,t,a,n),t.child;case 14:return vm(e,t,t.type,t.pendingProps,n);case 15:return gm(e,t,t.type,t.pendingProps,n);case 19:return Am(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(n=Cu(a,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=an(e.child,a),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return ym(e,t,n);case 24:return ml(t),a=nt(Xe),e===null?(r=wo(),r===null&&(r=Ue,c=xo(),r.pooledCache=c,c.refCount++,c!==null&&(r.pooledCacheLanes|=n),r=c),t.memoizedState={parent:a,cache:r},Ao(t),On(t,Xe,r)):((e.lanes&n)!==0&&(Mo(e,t),Pa(t,null,null,n),Ka()),r=e.memoizedState,c=t.memoizedState,r.parent!==a?(r={parent:a,cache:a},t.memoizedState=r,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=r),On(t,Xe,a)):(a=c.cache,On(t,Xe,a),a!==r.cache&&So(t,[Xe],n,!0))),Fe(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function mn(e){e.flags|=4}function Tm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Lh(t)){if(t=Dt.current,t!==null&&((we&4194048)===we?Qt!==null:(we&62914560)!==we&&(we&536870912)===0||t!==Qt))throw Qa=Ro,sd;e.flags|=8192}}function Ou(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?af():536870912,e.lanes|=t,ta|=t)}function tr(e,t){if(!Te)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function je(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)for(var r=e.child;r!==null;)n|=r.lanes|r.childLanes,a|=r.subtreeFlags&65011712,a|=r.flags&65011712,r.return=e,r=r.sibling;else for(r=e.child;r!==null;)n|=r.lanes|r.childLanes,a|=r.subtreeFlags,a|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function k0(e,t,n){var a=t.pendingProps;switch(vo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return je(t),null;case 1:return je(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),cn(Xe),at(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Ba(t)?mn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,rd())),je(t),null;case 26:return n=t.memoizedState,e===null?(mn(t),n!==null?(je(t),Tm(t,n)):(je(t),t.flags&=-16777217)):n?n!==e.memoizedState?(mn(t),je(t),Tm(t,n)):(je(t),t.flags&=-16777217):(e.memoizedProps!==a&&mn(t),je(t),t.flags&=-16777217),null;case 27:Yt(t),n=re.current;var r=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&mn(t);else{if(!a){if(t.stateNode===null)throw Error(i(166));return je(t),null}e=I.current,Ba(t)?ld(t):(e=Th(r,a,n),t.stateNode=e,mn(t))}return je(t),null;case 5:if(Yt(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&mn(t);else{if(!a){if(t.stateNode===null)throw Error(i(166));return je(t),null}if(e=I.current,Ba(t))ld(t);else{switch(r=qu(re.current),e){case 1:e=r.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=r.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=r.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?r.createElement("select",{is:a.is}):r.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?r.createElement(n,{is:a.is}):r.createElement(n)}}e[tt]=t,e[ot]=a;e:for(r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.tag!==27&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break e;for(;r.sibling===null;){if(r.return===null||r.return===t)break e;r=r.return}r.sibling.return=r.return,r=r.sibling}t.stateNode=e;e:switch(Ie(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&mn(t)}}return je(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&mn(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(i(166));if(e=re.current,Ba(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,r=rt,r!==null)switch(r.tag){case 27:case 5:a=r.memoizedProps}e[tt]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||Sh(e.nodeValue,n)),e||fl(t)}else e=qu(e).createTextNode(a),e[tt]=t,t.stateNode=e}return je(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(r=Ba(t),a!==null&&a.dehydrated!==null){if(e===null){if(!r)throw Error(i(318));if(r=t.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(i(317));r[tt]=t}else ka(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;je(t),r=!1}else r=rd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=r),r=!0;if(!r)return t.flags&256?(fn(t),t):(fn(t),null)}if(fn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=a!==null,e=e!==null&&e.memoizedState!==null,n){a=t.child,r=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(r=a.alternate.memoizedState.cachePool.pool);var c=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(c=a.memoizedState.cachePool.pool),c!==r&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Ou(t,t.updateQueue),je(t),null;case 4:return at(),e===null&&Cc(t.stateNode.containerInfo),je(t),null;case 10:return cn(t.type),je(t),null;case 19:if(F(Qe),r=t.memoizedState,r===null)return je(t),null;if(a=(t.flags&128)!==0,c=r.rendering,c===null)if(a)tr(r,!1);else{if(ke!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Ru(e),c!==null){for(t.flags|=128,tr(r,!1),e=c.updateQueue,t.updateQueue=e,Ou(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)td(n,e),n=n.sibling;return P(Qe,Qe.current&1|2),t.child}e=e.sibling}r.tail!==null&&Vt()>Nu&&(t.flags|=128,a=!0,tr(r,!1),t.lanes=4194304)}else{if(!a)if(e=Ru(c),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Ou(t,e),tr(r,!0),r.tail===null&&r.tailMode==="hidden"&&!c.alternate&&!Te)return je(t),null}else 2*Vt()-r.renderingStartTime>Nu&&n!==536870912&&(t.flags|=128,a=!0,tr(r,!1),t.lanes=4194304);r.isBackwards?(c.sibling=t.child,t.child=c):(e=r.last,e!==null?e.sibling=c:t.child=c,r.last=c)}return r.tail!==null?(t=r.tail,r.rendering=t,r.tail=t.sibling,r.renderingStartTime=Vt(),t.sibling=null,e=Qe.current,P(Qe,a?e&1|2:e&1),t):(je(t),null);case 22:case 23:return fn(t),_o(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(n&536870912)!==0&&(t.flags&128)===0&&(je(t),t.subtreeFlags&6&&(t.flags|=8192)):je(t),n=t.updateQueue,n!==null&&Ou(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&F(hl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),cn(Xe),je(t),null;case 25:return null;case 30:return null}throw Error(i(156,t.tag))}function G0(e,t){switch(vo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return cn(Xe),at(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Yt(t),null;case 13:if(fn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));ka()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(Qe),null;case 4:return at(),null;case 10:return cn(t.type),null;case 22:case 23:return fn(t),_o(),e!==null&&F(hl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return cn(Xe),null;case 25:return null;default:return null}}function Cm(e,t){switch(vo(t),t.tag){case 3:cn(Xe),at();break;case 26:case 27:case 5:Yt(t);break;case 4:at();break;case 13:fn(t);break;case 19:F(Qe);break;case 10:cn(t.type);break;case 22:case 23:fn(t),_o(),e!==null&&F(hl);break;case 24:cn(Xe)}}function nr(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var r=a.next;n=r;do{if((n.tag&e)===e){a=void 0;var c=n.create,d=n.inst;a=c(),d.destroy=a}n=n.next}while(n!==r)}}catch(b){ze(t,t.return,b)}}function jn(e,t,n){try{var a=t.updateQueue,r=a!==null?a.lastEffect:null;if(r!==null){var c=r.next;a=c;do{if((a.tag&e)===e){var d=a.inst,b=d.destroy;if(b!==void 0){d.destroy=void 0,r=t;var x=n,U=b;try{U()}catch(G){ze(r,x,G)}}}a=a.next}while(a!==c)}}catch(G){ze(t,t.return,G)}}function Om(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{vd(t,n)}catch(a){ze(e,e.return,a)}}}function _m(e,t,n){n.props=vl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){ze(e,t,a)}}function lr(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof n=="function"?e.refCleanup=n(a):n.current=a}}catch(r){ze(e,t,r)}}function Zt(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(r){ze(e,t,r)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(r){ze(e,t,r)}else n.current=null}function Dm(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(r){ze(e,e.return,r)}}function ac(e,t,n){try{var a=e.stateNode;ib(a,e.type,n,t),a[ot]=t}catch(r){ze(e,e.return,r)}}function Nm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Xn(e.type)||e.tag===4}function rc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Nm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Xn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function uc(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Yu));else if(a!==4&&(a===27&&Xn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(uc(e,t,n),e=e.sibling;e!==null;)uc(e,t,n),e=e.sibling}function _u(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&Xn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(_u(e,t,n),e=e.sibling;e!==null;)_u(e,t,n),e=e.sibling}function zm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,r=t.attributes;r.length;)t.removeAttributeNode(r[0]);Ie(t,a,n),t[tt]=e,t[ot]=n}catch(c){ze(e,e.return,c)}}var hn=!1,Ye=!1,ic=!1,Um=typeof WeakSet=="function"?WeakSet:Set,$e=null;function Y0(e,t){if(e=e.containerInfo,Dc=Pu,e=Qf(e),ao(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var r=a.anchorOffset,c=a.focusNode;a=a.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var d=0,b=-1,x=-1,U=0,G=0,Q=e,L=null;t:for(;;){for(var j;Q!==n||r!==0&&Q.nodeType!==3||(b=d+r),Q!==c||a!==0&&Q.nodeType!==3||(x=d+a),Q.nodeType===3&&(d+=Q.nodeValue.length),(j=Q.firstChild)!==null;)L=Q,Q=j;for(;;){if(Q===e)break t;if(L===n&&++U===r&&(b=d),L===c&&++G===a&&(x=d),(j=Q.nextSibling)!==null)break;Q=L,L=Q.parentNode}Q=j}n=b===-1||x===-1?null:{start:b,end:x}}else n=null}n=n||{start:0,end:0}}else n=null;for(Nc={focusedElem:e,selectionRange:n},Pu=!1,$e=t;$e!==null;)if(t=$e,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,$e=e;else for(;$e!==null;){switch(t=$e,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,r=c.memoizedProps,c=c.memoizedState,a=n.stateNode;try{var ce=vl(n.type,r,n.elementType===n.type);e=a.getSnapshotBeforeUpdate(ce,c),a.__reactInternalSnapshotBeforeUpdate=e}catch(ie){ze(n,n.return,ie)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Lc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Lc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(i(163))}if(e=t.sibling,e!==null){e.return=t.return,$e=e;break}$e=t.return}}function Lm(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:Hn(e,n),a&4&&nr(5,n);break;case 1:if(Hn(e,n),a&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(d){ze(n,n.return,d)}else{var r=vl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(r,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){ze(n,n.return,d)}}a&64&&Om(n),a&512&&lr(n,n.return);break;case 3:if(Hn(e,n),a&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{vd(e,t)}catch(d){ze(n,n.return,d)}}break;case 27:t===null&&a&4&&zm(n);case 26:case 5:Hn(e,n),t===null&&a&4&&Dm(n),a&512&&lr(n,n.return);break;case 12:Hn(e,n);break;case 13:Hn(e,n),a&4&&Bm(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=J0.bind(null,n),hb(e,n))));break;case 22:if(a=n.memoizedState!==null||hn,!a){t=t!==null&&t.memoizedState!==null||Ye,r=hn;var c=Ye;hn=a,(Ye=t)&&!c?Bn(e,n,(n.subtreeFlags&8772)!==0):Hn(e,n),hn=r,Ye=c}break;case 30:break;default:Hn(e,n)}}function jm(e){var t=e.alternate;t!==null&&(e.alternate=null,jm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&ki(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Le=null,ft=!1;function pn(e,t,n){for(n=n.child;n!==null;)Hm(e,t,n),n=n.sibling}function Hm(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(Ra,n)}catch{}switch(n.tag){case 26:Ye||Zt(n,t),pn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ye||Zt(n,t);var a=Le,r=ft;Xn(n.type)&&(Le=n.stateNode,ft=!1),pn(e,t,n),dr(n.stateNode),Le=a,ft=r;break;case 5:Ye||Zt(n,t);case 6:if(a=Le,r=ft,Le=null,pn(e,t,n),Le=a,ft=r,Le!==null)if(ft)try{(Le.nodeType===9?Le.body:Le.nodeName==="HTML"?Le.ownerDocument.body:Le).removeChild(n.stateNode)}catch(c){ze(n,t,c)}else try{Le.removeChild(n.stateNode)}catch(c){ze(n,t,c)}break;case 18:Le!==null&&(ft?(e=Le,Ah(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Sr(e)):Ah(Le,n.stateNode));break;case 4:a=Le,r=ft,Le=n.stateNode.containerInfo,ft=!0,pn(e,t,n),Le=a,ft=r;break;case 0:case 11:case 14:case 15:Ye||jn(2,n,t),Ye||jn(4,n,t),pn(e,t,n);break;case 1:Ye||(Zt(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&_m(n,t,a)),pn(e,t,n);break;case 21:pn(e,t,n);break;case 22:Ye=(a=Ye)||n.memoizedState!==null,pn(e,t,n),Ye=a;break;default:pn(e,t,n)}}function Bm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Sr(e)}catch(n){ze(t,t.return,n)}}function q0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Um),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Um),t;default:throw Error(i(435,e.tag))}}function oc(e,t){var n=q0(e);t.forEach(function(a){var r=F0.bind(null,e,a);n.has(a)||(n.add(a),a.then(r,r))})}function yt(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var r=n[a],c=e,d=t,b=d;e:for(;b!==null;){switch(b.tag){case 27:if(Xn(b.type)){Le=b.stateNode,ft=!1;break e}break;case 5:Le=b.stateNode,ft=!1;break e;case 3:case 4:Le=b.stateNode.containerInfo,ft=!0;break e}b=b.return}if(Le===null)throw Error(i(160));Hm(c,d,r),Le=null,ft=!1,c=r.alternate,c!==null&&(c.return=null),r.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)km(t,e),t=t.sibling}var jt=null;function km(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:yt(t,e),bt(e),a&4&&(jn(3,e,e.return),nr(3,e),jn(5,e,e.return));break;case 1:yt(t,e),bt(e),a&512&&(Ye||n===null||Zt(n,n.return)),a&64&&hn&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var r=jt;if(yt(t,e),bt(e),a&512&&(Ye||n===null||Zt(n,n.return)),a&4){var c=n!==null?n.memoizedState:null;if(a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,r=r.ownerDocument||r;t:switch(a){case"title":c=r.getElementsByTagName("title")[0],(!c||c[Ta]||c[tt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=r.createElement(a),r.head.insertBefore(c,r.querySelector("head > title"))),Ie(c,a,n),c[tt]=e,Ke(c),a=c;break e;case"link":var d=zh("link","href",r).get(a+(n.href||""));if(d){for(var b=0;b<d.length;b++)if(c=d[b],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){d.splice(b,1);break t}}c=r.createElement(a),Ie(c,a,n),r.head.appendChild(c);break;case"meta":if(d=zh("meta","content",r).get(a+(n.content||""))){for(b=0;b<d.length;b++)if(c=d[b],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){d.splice(b,1);break t}}c=r.createElement(a),Ie(c,a,n),r.head.appendChild(c);break;default:throw Error(i(468,a))}c[tt]=e,Ke(c),a=c}e.stateNode=a}else Uh(r,e.type,e.stateNode);else e.stateNode=Nh(r,a,e.memoizedProps);else c!==a?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,a===null?Uh(r,e.type,e.stateNode):Nh(r,a,e.memoizedProps)):a===null&&e.stateNode!==null&&ac(e,e.memoizedProps,n.memoizedProps)}break;case 27:yt(t,e),bt(e),a&512&&(Ye||n===null||Zt(n,n.return)),n!==null&&a&4&&ac(e,e.memoizedProps,n.memoizedProps);break;case 5:if(yt(t,e),bt(e),a&512&&(Ye||n===null||Zt(n,n.return)),e.flags&32){r=e.stateNode;try{zl(r,"")}catch(j){ze(e,e.return,j)}}a&4&&e.stateNode!=null&&(r=e.memoizedProps,ac(e,r,n!==null?n.memoizedProps:r)),a&1024&&(ic=!0);break;case 6:if(yt(t,e),bt(e),a&4){if(e.stateNode===null)throw Error(i(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(j){ze(e,e.return,j)}}break;case 3:if(Qu=null,r=jt,jt=Vu(t.containerInfo),yt(t,e),jt=r,bt(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{Sr(t.containerInfo)}catch(j){ze(e,e.return,j)}ic&&(ic=!1,Gm(e));break;case 4:a=jt,jt=Vu(e.stateNode.containerInfo),yt(t,e),bt(e),jt=a;break;case 12:yt(t,e),bt(e);break;case 13:yt(t,e),bt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(hc=Vt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,oc(e,a)));break;case 22:r=e.memoizedState!==null;var x=n!==null&&n.memoizedState!==null,U=hn,G=Ye;if(hn=U||r,Ye=G||x,yt(t,e),Ye=G,hn=U,bt(e),a&8192)e:for(t=e.stateNode,t._visibility=r?t._visibility&-2:t._visibility|1,r&&(n===null||x||hn||Ye||gl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){x=n=t;try{if(c=x.stateNode,r)d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{b=x.stateNode;var Q=x.memoizedProps.style,L=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;b.style.display=L==null||typeof L=="boolean"?"":(""+L).trim()}}catch(j){ze(x,x.return,j)}}}else if(t.tag===6){if(n===null){x=t;try{x.stateNode.nodeValue=r?"":x.memoizedProps}catch(j){ze(x,x.return,j)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,oc(e,n))));break;case 19:yt(t,e),bt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,oc(e,a)));break;case 30:break;case 21:break;default:yt(t,e),bt(e)}}function bt(e){var t=e.flags;if(t&2){try{for(var n,a=e.return;a!==null;){if(Nm(a)){n=a;break}a=a.return}if(n==null)throw Error(i(160));switch(n.tag){case 27:var r=n.stateNode,c=rc(e);_u(e,c,r);break;case 5:var d=n.stateNode;n.flags&32&&(zl(d,""),n.flags&=-33);var b=rc(e);_u(e,b,d);break;case 3:case 4:var x=n.stateNode.containerInfo,U=rc(e);uc(e,U,x);break;default:throw Error(i(161))}}catch(G){ze(e,e.return,G)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Gm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Gm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Hn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Lm(e,t.alternate,t),t=t.sibling}function gl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:jn(4,t,t.return),gl(t);break;case 1:Zt(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&_m(t,t.return,n),gl(t);break;case 27:dr(t.stateNode);case 26:case 5:Zt(t,t.return),gl(t);break;case 22:t.memoizedState===null&&gl(t);break;case 30:gl(t);break;default:gl(t)}e=e.sibling}}function Bn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,r=e,c=t,d=c.flags;switch(c.tag){case 0:case 11:case 15:Bn(r,c,n),nr(4,c);break;case 1:if(Bn(r,c,n),a=c,r=a.stateNode,typeof r.componentDidMount=="function")try{r.componentDidMount()}catch(U){ze(a,a.return,U)}if(a=c,r=a.updateQueue,r!==null){var b=a.stateNode;try{var x=r.shared.hiddenCallbacks;if(x!==null)for(r.shared.hiddenCallbacks=null,r=0;r<x.length;r++)pd(x[r],b)}catch(U){ze(a,a.return,U)}}n&&d&64&&Om(c),lr(c,c.return);break;case 27:zm(c);case 26:case 5:Bn(r,c,n),n&&a===null&&d&4&&Dm(c),lr(c,c.return);break;case 12:Bn(r,c,n);break;case 13:Bn(r,c,n),n&&d&4&&Bm(r,c);break;case 22:c.memoizedState===null&&Bn(r,c,n),lr(c,c.return);break;case 30:break;default:Bn(r,c,n)}t=t.sibling}}function cc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&qa(n))}function sc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&qa(e))}function Kt(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ym(e,t,n,a),t=t.sibling}function Ym(e,t,n,a){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Kt(e,t,n,a),r&2048&&nr(9,t);break;case 1:Kt(e,t,n,a);break;case 3:Kt(e,t,n,a),r&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&qa(e)));break;case 12:if(r&2048){Kt(e,t,n,a),e=t.stateNode;try{var c=t.memoizedProps,d=c.id,b=c.onPostCommit;typeof b=="function"&&b(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(x){ze(t,t.return,x)}}else Kt(e,t,n,a);break;case 13:Kt(e,t,n,a);break;case 23:break;case 22:c=t.stateNode,d=t.alternate,t.memoizedState!==null?c._visibility&2?Kt(e,t,n,a):ar(e,t):c._visibility&2?Kt(e,t,n,a):(c._visibility|=2,Wl(e,t,n,a,(t.subtreeFlags&10256)!==0)),r&2048&&cc(d,t);break;case 24:Kt(e,t,n,a),r&2048&&sc(t.alternate,t);break;default:Kt(e,t,n,a)}}function Wl(e,t,n,a,r){for(r=r&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,d=t,b=n,x=a,U=d.flags;switch(d.tag){case 0:case 11:case 15:Wl(c,d,b,x,r),nr(8,d);break;case 23:break;case 22:var G=d.stateNode;d.memoizedState!==null?G._visibility&2?Wl(c,d,b,x,r):ar(c,d):(G._visibility|=2,Wl(c,d,b,x,r)),r&&U&2048&&cc(d.alternate,d);break;case 24:Wl(c,d,b,x,r),r&&U&2048&&sc(d.alternate,d);break;default:Wl(c,d,b,x,r)}t=t.sibling}}function ar(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,r=a.flags;switch(a.tag){case 22:ar(n,a),r&2048&&cc(a.alternate,a);break;case 24:ar(n,a),r&2048&&sc(a.alternate,a);break;default:ar(n,a)}t=t.sibling}}var rr=8192;function Il(e){if(e.subtreeFlags&rr)for(e=e.child;e!==null;)qm(e),e=e.sibling}function qm(e){switch(e.tag){case 26:Il(e),e.flags&rr&&e.memoizedState!==null&&Tb(jt,e.memoizedState,e.memoizedProps);break;case 5:Il(e);break;case 3:case 4:var t=jt;jt=Vu(e.stateNode.containerInfo),Il(e),jt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=rr,rr=16777216,Il(e),rr=t):Il(e));break;default:Il(e)}}function Vm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ur(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];$e=a,Qm(a,e)}Vm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Xm(e),e=e.sibling}function Xm(e){switch(e.tag){case 0:case 11:case 15:ur(e),e.flags&2048&&jn(9,e,e.return);break;case 3:ur(e);break;case 12:ur(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Du(e)):ur(e);break;default:ur(e)}}function Du(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];$e=a,Qm(a,e)}Vm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:jn(8,t,t.return),Du(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Du(t));break;default:Du(t)}e=e.sibling}}function Qm(e,t){for(;$e!==null;){var n=$e;switch(n.tag){case 0:case 11:case 15:jn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:qa(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,$e=a;else e:for(n=e;$e!==null;){a=$e;var r=a.sibling,c=a.return;if(jm(a),a===n){$e=null;break e}if(r!==null){r.return=c,$e=r;break e}$e=c}}}var V0={getCacheForType:function(e){var t=nt(Xe),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},X0=typeof WeakMap=="function"?WeakMap:Map,Ce=0,Ue=null,Se=null,we=0,Oe=0,St=null,kn=!1,ea=!1,fc=!1,vn=0,ke=0,Gn=0,yl=0,dc=0,Nt=0,ta=0,ir=null,dt=null,mc=!1,hc=0,Nu=1/0,zu=null,Yn=null,We=0,qn=null,na=null,la=0,pc=0,vc=null,Zm=null,or=0,gc=null;function xt(){if((Ce&2)!==0&&we!==0)return we&-we;if(z.T!==null){var e=Xl;return e!==0?e:Rc()}return of()}function Km(){Nt===0&&(Nt=(we&536870912)===0||Te?lf():536870912);var e=Dt.current;return e!==null&&(e.flags|=32),Nt}function Et(e,t,n){(e===Ue&&(Oe===2||Oe===9)||e.cancelPendingCommit!==null)&&(aa(e,0),Vn(e,we,Nt,!1)),Ma(e,n),((Ce&2)===0||e!==Ue)&&(e===Ue&&((Ce&2)===0&&(yl|=n),ke===4&&Vn(e,we,Nt,!1)),Pt(e))}function Pm(e,t,n){if((Ce&6)!==0)throw Error(i(327));var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Aa(e,t),r=a?K0(e,t):Sc(e,t,!0),c=a;do{if(r===0){ea&&!a&&Vn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!Q0(n)){r=Sc(e,t,!1),c=!1;continue}if(r===2){if(c=t,e.errorRecoveryDisabledLanes&c)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var b=e;r=ir;var x=b.current.memoizedState.isDehydrated;if(x&&(aa(b,d).flags|=256),d=Sc(b,d,!1),d!==2){if(fc&&!x){b.errorRecoveryDisabledLanes|=c,yl|=c,r=4;break e}c=dt,dt=r,c!==null&&(dt===null?dt=c:dt.push.apply(dt,c))}r=d}if(c=!1,r!==2)continue}}if(r===1){aa(e,0),Vn(e,t,0,!0);break}e:{switch(a=e,c=r,c){case 0:case 1:throw Error(i(345));case 4:if((t&4194048)!==t)break;case 6:Vn(a,t,Nt,!kn);break e;case 2:dt=null;break;case 3:case 5:break;default:throw Error(i(329))}if((t&62914560)===t&&(r=hc+300-Vt(),10<r)){if(Vn(a,t,Nt,!kn),Xr(a,0,!0)!==0)break e;a.timeoutHandle=wh($m.bind(null,a,n,dt,zu,mc,t,Nt,yl,ta,kn,c,2,-0,0),r);break e}$m(a,n,dt,zu,mc,t,Nt,yl,ta,kn,c,0,-0,0)}}break}while(!0);Pt(e)}function $m(e,t,n,a,r,c,d,b,x,U,G,Q,L,j){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(pr={stylesheets:null,count:0,unsuspend:Mb},qm(t),Q=Cb(),Q!==null)){e.cancelPendingCommit=Q(nh.bind(null,e,t,c,n,a,r,d,b,x,G,1,L,j)),Vn(e,c,d,!U);return}nh(e,t,c,n,a,r,d,b,x)}function Q0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var r=n[a],c=r.getSnapshot;r=r.value;try{if(!vt(c(),r))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Vn(e,t,n,a){t&=~dc,t&=~yl,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var r=t;0<r;){var c=31-pt(r),d=1<<c;a[c]=-1,r&=~d}n!==0&&rf(e,n,t)}function Uu(){return(Ce&6)===0?(cr(0),!1):!0}function yc(){if(Se!==null){if(Oe===0)var e=Se.return;else e=Se,on=dl=null,Lo(e),Jl=null,Ia=0,e=Se;for(;e!==null;)Cm(e.alternate,e),e=e.return;Se=null}}function aa(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,cb(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),yc(),Ue=e,Se=n=an(e.current,null),we=t,Oe=0,St=null,kn=!1,ea=Aa(e,t),fc=!1,ta=Nt=dc=yl=Gn=ke=0,dt=ir=null,mc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var r=31-pt(a),c=1<<r;t|=e[r],a&=~c}return vn=t,nu(),n}function Jm(e,t){pe=null,z.H=xu,t===Xa||t===fu?(t=md(),Oe=3):t===sd?(t=md(),Oe=4):Oe=t===hm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,St=t,Se===null&&(ke=1,Mu(e,Tt(t,e.current)))}function Fm(){var e=z.H;return z.H=xu,e===null?xu:e}function Wm(){var e=z.A;return z.A=V0,e}function bc(){ke=4,kn||(we&4194048)!==we&&Dt.current!==null||(ea=!0),(Gn&134217727)===0&&(yl&134217727)===0||Ue===null||Vn(Ue,we,Nt,!1)}function Sc(e,t,n){var a=Ce;Ce|=2;var r=Fm(),c=Wm();(Ue!==e||we!==t)&&(zu=null,aa(e,t)),t=!1;var d=ke;e:do try{if(Oe!==0&&Se!==null){var b=Se,x=St;switch(Oe){case 8:yc(),d=6;break e;case 3:case 2:case 9:case 6:Dt.current===null&&(t=!0);var U=Oe;if(Oe=0,St=null,ra(e,b,x,U),n&&ea){d=0;break e}break;default:U=Oe,Oe=0,St=null,ra(e,b,x,U)}}Z0(),d=ke;break}catch(G){Jm(e,G)}while(!0);return t&&e.shellSuspendCounter++,on=dl=null,Ce=a,z.H=r,z.A=c,Se===null&&(Ue=null,we=0,nu()),d}function Z0(){for(;Se!==null;)Im(Se)}function K0(e,t){var n=Ce;Ce|=2;var a=Fm(),r=Wm();Ue!==e||we!==t?(zu=null,Nu=Vt()+500,aa(e,t)):ea=Aa(e,t);e:do try{if(Oe!==0&&Se!==null){t=Se;var c=St;t:switch(Oe){case 1:Oe=0,St=null,ra(e,t,c,1);break;case 2:case 9:if(fd(c)){Oe=0,St=null,eh(t);break}t=function(){Oe!==2&&Oe!==9||Ue!==e||(Oe=7),Pt(e)},c.then(t,t);break e;case 3:Oe=7;break e;case 4:Oe=5;break e;case 7:fd(c)?(Oe=0,St=null,eh(t)):(Oe=0,St=null,ra(e,t,c,7));break;case 5:var d=null;switch(Se.tag){case 26:d=Se.memoizedState;case 5:case 27:var b=Se;if(!d||Lh(d)){Oe=0,St=null;var x=b.sibling;if(x!==null)Se=x;else{var U=b.return;U!==null?(Se=U,Lu(U)):Se=null}break t}}Oe=0,St=null,ra(e,t,c,5);break;case 6:Oe=0,St=null,ra(e,t,c,6);break;case 8:yc(),ke=6;break e;default:throw Error(i(462))}}P0();break}catch(G){Jm(e,G)}while(!0);return on=dl=null,z.H=a,z.A=r,Ce=n,Se!==null?0:(Ue=null,we=0,nu(),ke)}function P0(){for(;Se!==null&&!vy();)Im(Se)}function Im(e){var t=Mm(e.alternate,e,vn);e.memoizedProps=e.pendingProps,t===null?Lu(e):Se=t}function eh(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Sm(n,t,t.pendingProps,t.type,void 0,we);break;case 11:t=Sm(n,t,t.pendingProps,t.type.render,t.ref,we);break;case 5:Lo(t);default:Cm(n,t),t=Se=td(t,vn),t=Mm(n,t,vn)}e.memoizedProps=e.pendingProps,t===null?Lu(e):Se=t}function ra(e,t,n,a){on=dl=null,Lo(t),Jl=null,Ia=0;var r=t.return;try{if(H0(e,r,t,n,we)){ke=1,Mu(e,Tt(n,e.current)),Se=null;return}}catch(c){if(r!==null)throw Se=r,c;ke=1,Mu(e,Tt(n,e.current)),Se=null;return}t.flags&32768?(Te||a===1?e=!0:ea||(we&536870912)!==0?e=!1:(kn=e=!0,(a===2||a===9||a===3||a===6)&&(a=Dt.current,a!==null&&a.tag===13&&(a.flags|=16384))),th(t,e)):Lu(t)}function Lu(e){var t=e;do{if((t.flags&32768)!==0){th(t,kn);return}e=t.return;var n=k0(t.alternate,t,vn);if(n!==null){Se=n;return}if(t=t.sibling,t!==null){Se=t;return}Se=t=e}while(t!==null);ke===0&&(ke=5)}function th(e,t){do{var n=G0(e.alternate,e);if(n!==null){n.flags&=32767,Se=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){Se=e;return}Se=e=n}while(e!==null);ke=6,Se=null}function nh(e,t,n,a,r,c,d,b,x){e.cancelPendingCommit=null;do ju();while(We!==0);if((Ce&6)!==0)throw Error(i(327));if(t!==null){if(t===e.current)throw Error(i(177));if(c=t.lanes|t.childLanes,c|=co,My(e,n,c,d,b,x),e===Ue&&(Se=Ue=null,we=0),na=t,qn=e,la=n,pc=c,vc=r,Zm=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,W0(Yr,function(){return ih(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=z.T,z.T=null,r=K.p,K.p=2,d=Ce,Ce|=4;try{Y0(e,t,n)}finally{Ce=d,K.p=r,z.T=a}}We=1,lh(),ah(),rh()}}function lh(){if(We===1){We=0;var e=qn,t=na,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=z.T,z.T=null;var a=K.p;K.p=2;var r=Ce;Ce|=4;try{km(t,e);var c=Nc,d=Qf(e.containerInfo),b=c.focusedElem,x=c.selectionRange;if(d!==b&&b&&b.ownerDocument&&Xf(b.ownerDocument.documentElement,b)){if(x!==null&&ao(b)){var U=x.start,G=x.end;if(G===void 0&&(G=U),"selectionStart"in b)b.selectionStart=U,b.selectionEnd=Math.min(G,b.value.length);else{var Q=b.ownerDocument||document,L=Q&&Q.defaultView||window;if(L.getSelection){var j=L.getSelection(),ce=b.textContent.length,ie=Math.min(x.start,ce),Ne=x.end===void 0?ie:Math.min(x.end,ce);!j.extend&&ie>Ne&&(d=Ne,Ne=ie,ie=d);var O=Vf(b,ie),M=Vf(b,Ne);if(O&&M&&(j.rangeCount!==1||j.anchorNode!==O.node||j.anchorOffset!==O.offset||j.focusNode!==M.node||j.focusOffset!==M.offset)){var D=Q.createRange();D.setStart(O.node,O.offset),j.removeAllRanges(),ie>Ne?(j.addRange(D),j.extend(M.node,M.offset)):(D.setEnd(M.node,M.offset),j.addRange(D))}}}}for(Q=[],j=b;j=j.parentNode;)j.nodeType===1&&Q.push({element:j,left:j.scrollLeft,top:j.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<Q.length;b++){var q=Q[b];q.element.scrollLeft=q.left,q.element.scrollTop=q.top}}Pu=!!Dc,Nc=Dc=null}finally{Ce=r,K.p=a,z.T=n}}e.current=t,We=2}}function ah(){if(We===2){We=0;var e=qn,t=na,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=z.T,z.T=null;var a=K.p;K.p=2;var r=Ce;Ce|=4;try{Lm(e,t.alternate,t)}finally{Ce=r,K.p=a,z.T=n}}We=3}}function rh(){if(We===4||We===3){We=0,gy();var e=qn,t=na,n=la,a=Zm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?We=5:(We=0,na=qn=null,uh(e,e.pendingLanes));var r=e.pendingLanes;if(r===0&&(Yn=null),Hi(n),t=t.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(Ra,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=z.T,r=K.p,K.p=2,z.T=null;try{for(var c=e.onRecoverableError,d=0;d<a.length;d++){var b=a[d];c(b.value,{componentStack:b.stack})}}finally{z.T=t,K.p=r}}(la&3)!==0&&ju(),Pt(e),r=e.pendingLanes,(n&4194090)!==0&&(r&42)!==0?e===gc?or++:(or=0,gc=e):or=0,cr(0)}}function uh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,qa(t)))}function ju(e){return lh(),ah(),rh(),ih()}function ih(){if(We!==5)return!1;var e=qn,t=pc;pc=0;var n=Hi(la),a=z.T,r=K.p;try{K.p=32>n?32:n,z.T=null,n=vc,vc=null;var c=qn,d=la;if(We=0,na=qn=null,la=0,(Ce&6)!==0)throw Error(i(331));var b=Ce;if(Ce|=4,Xm(c.current),Ym(c,c.current,d,n),Ce=b,cr(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(Ra,c)}catch{}return!0}finally{K.p=r,z.T=a,uh(e,t)}}function oh(e,t,n){t=Tt(n,t),t=$o(e.stateNode,t,2),e=Nn(e,t,2),e!==null&&(Ma(e,2),Pt(e))}function ze(e,t,n){if(e.tag===3)oh(e,e,n);else for(;t!==null;){if(t.tag===3){oh(t,e,n);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Yn===null||!Yn.has(a))){e=Tt(n,e),n=dm(2),a=Nn(t,n,2),a!==null&&(mm(n,a,t,e),Ma(a,2),Pt(a));break}}t=t.return}}function xc(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new X0;var r=new Set;a.set(t,r)}else r=a.get(t),r===void 0&&(r=new Set,a.set(t,r));r.has(n)||(fc=!0,r.add(n),e=$0.bind(null,e,t,n),t.then(e,e))}function $0(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Ue===e&&(we&n)===n&&(ke===4||ke===3&&(we&62914560)===we&&300>Vt()-hc?(Ce&2)===0&&aa(e,0):dc|=n,ta===we&&(ta=0)),Pt(e)}function ch(e,t){t===0&&(t=af()),e=Gl(e,t),e!==null&&(Ma(e,t),Pt(e))}function J0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ch(e,n)}function F0(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,r=e.memoizedState;r!==null&&(n=r.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(i(314))}a!==null&&a.delete(t),ch(e,n)}function W0(e,t){return An(e,t)}var Hu=null,ua=null,Ec=!1,Bu=!1,wc=!1,bl=0;function Pt(e){e!==ua&&e.next===null&&(ua===null?Hu=ua=e:ua=ua.next=e),Bu=!0,Ec||(Ec=!0,eb())}function cr(e,t){if(!wc&&Bu){wc=!0;do for(var n=!1,a=Hu;a!==null;){if(e!==0){var r=a.pendingLanes;if(r===0)var c=0;else{var d=a.suspendedLanes,b=a.pingedLanes;c=(1<<31-pt(42|e)+1)-1,c&=r&~(d&~b),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,mh(a,c))}else c=we,c=Xr(a,a===Ue?c:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(c&3)===0||Aa(a,c)||(n=!0,mh(a,c));a=a.next}while(n);wc=!1}}function I0(){sh()}function sh(){Bu=Ec=!1;var e=0;bl!==0&&(ob()&&(e=bl),bl=0);for(var t=Vt(),n=null,a=Hu;a!==null;){var r=a.next,c=fh(a,t);c===0?(a.next=null,n===null?Hu=r:n.next=r,r===null&&(ua=n)):(n=a,(e!==0||(c&3)!==0)&&(Bu=!0)),a=r}cr(e)}function fh(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,r=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var d=31-pt(c),b=1<<d,x=r[d];x===-1?((b&n)===0||(b&a)!==0)&&(r[d]=Ay(b,t)):x<=t&&(e.expiredLanes|=b),c&=~b}if(t=Ue,n=we,n=Xr(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,n===0||e===t&&(Oe===2||Oe===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Ui(a),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Aa(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(a!==null&&Ui(a),Hi(n)){case 2:case 8:n=tf;break;case 32:n=Yr;break;case 268435456:n=nf;break;default:n=Yr}return a=dh.bind(null,e),n=An(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&a!==null&&Ui(a),e.callbackPriority=2,e.callbackNode=null,2}function dh(e,t){if(We!==0&&We!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ju()&&e.callbackNode!==n)return null;var a=we;return a=Xr(e,e===Ue?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Pm(e,a,t),fh(e,Vt()),e.callbackNode!=null&&e.callbackNode===n?dh.bind(null,e):null)}function mh(e,t){if(ju())return null;Pm(e,t,!0)}function eb(){sb(function(){(Ce&6)!==0?An(ef,I0):sh()})}function Rc(){return bl===0&&(bl=lf()),bl}function hh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:$r(""+e)}function ph(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function tb(e,t,n,a,r){if(t==="submit"&&n&&n.stateNode===r){var c=hh((r[ot]||null).action),d=a.submitter;d&&(t=(t=d[ot]||null)?hh(t.formAction):d.getAttribute("formAction"),t!==null&&(c=t,d=null));var b=new Ir("action","action",null,a,r);e.push({event:b,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(bl!==0){var x=d?ph(r,d):new FormData(r);Xo(n,{pending:!0,data:x,method:r.method,action:c},null,x)}}else typeof c=="function"&&(b.preventDefault(),x=d?ph(r,d):new FormData(r),Xo(n,{pending:!0,data:x,method:r.method,action:c},c,x))},currentTarget:r}]})}}for(var Ac=0;Ac<oo.length;Ac++){var Mc=oo[Ac],nb=Mc.toLowerCase(),lb=Mc[0].toUpperCase()+Mc.slice(1);Lt(nb,"on"+lb)}Lt(Pf,"onAnimationEnd"),Lt($f,"onAnimationIteration"),Lt(Jf,"onAnimationStart"),Lt("dblclick","onDoubleClick"),Lt("focusin","onFocus"),Lt("focusout","onBlur"),Lt(S0,"onTransitionRun"),Lt(x0,"onTransitionStart"),Lt(E0,"onTransitionCancel"),Lt(Ff,"onTransitionEnd"),_l("onMouseEnter",["mouseout","mouseover"]),_l("onMouseLeave",["mouseout","mouseover"]),_l("onPointerEnter",["pointerout","pointerover"]),_l("onPointerLeave",["pointerout","pointerover"]),ll("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ll("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ll("onBeforeInput",["compositionend","keypress","textInput","paste"]),ll("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ll("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ll("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var sr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ab=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(sr));function vh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n],r=a.event;a=a.listeners;e:{var c=void 0;if(t)for(var d=a.length-1;0<=d;d--){var b=a[d],x=b.instance,U=b.currentTarget;if(b=b.listener,x!==c&&r.isPropagationStopped())break e;c=b,r.currentTarget=U;try{c(r)}catch(G){Au(G)}r.currentTarget=null,c=x}else for(d=0;d<a.length;d++){if(b=a[d],x=b.instance,U=b.currentTarget,b=b.listener,x!==c&&r.isPropagationStopped())break e;c=b,r.currentTarget=U;try{c(r)}catch(G){Au(G)}r.currentTarget=null,c=x}}}}function xe(e,t){var n=t[Bi];n===void 0&&(n=t[Bi]=new Set);var a=e+"__bubble";n.has(a)||(gh(t,e,2,!1),n.add(a))}function Tc(e,t,n){var a=0;t&&(a|=4),gh(n,e,a,t)}var ku="_reactListening"+Math.random().toString(36).slice(2);function Cc(e){if(!e[ku]){e[ku]=!0,sf.forEach(function(n){n!=="selectionchange"&&(ab.has(n)||Tc(n,!1,e),Tc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ku]||(t[ku]=!0,Tc("selectionchange",!1,t))}}function gh(e,t,n,a){switch(Yh(t)){case 2:var r=Db;break;case 8:r=Nb;break;default:r=qc}n=r.bind(null,t,n,e),r=void 0,!$i||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(r=!0),a?r!==void 0?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):r!==void 0?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function Oc(e,t,n,a,r){var c=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var d=a.tag;if(d===3||d===4){var b=a.stateNode.containerInfo;if(b===r)break;if(d===4)for(d=a.return;d!==null;){var x=d.tag;if((x===3||x===4)&&d.stateNode.containerInfo===r)return;d=d.return}for(;b!==null;){if(d=Tl(b),d===null)return;if(x=d.tag,x===5||x===6||x===26||x===27){a=c=d;continue e}b=b.parentNode}}a=a.return}Rf(function(){var U=c,G=Ki(n),Q=[];e:{var L=Wf.get(e);if(L!==void 0){var j=Ir,ce=e;switch(e){case"keypress":if(Fr(n)===0)break e;case"keydown":case"keyup":j=Wy;break;case"focusin":ce="focus",j=Ii;break;case"focusout":ce="blur",j=Ii;break;case"beforeblur":case"afterblur":j=Ii;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=Tf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=Gy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=t0;break;case Pf:case $f:case Jf:j=Vy;break;case Ff:j=l0;break;case"scroll":case"scrollend":j=By;break;case"wheel":j=r0;break;case"copy":case"cut":case"paste":j=Qy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=Of;break;case"toggle":case"beforetoggle":j=i0}var ie=(t&4)!==0,Ne=!ie&&(e==="scroll"||e==="scrollend"),O=ie?L!==null?L+"Capture":null:L;ie=[];for(var M=U,D;M!==null;){var q=M;if(D=q.stateNode,q=q.tag,q!==5&&q!==26&&q!==27||D===null||O===null||(q=Oa(M,O),q!=null&&ie.push(fr(M,q,D))),Ne)break;M=M.return}0<ie.length&&(L=new j(L,ce,null,n,G),Q.push({event:L,listeners:ie}))}}if((t&7)===0){e:{if(L=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",L&&n!==Zi&&(ce=n.relatedTarget||n.fromElement)&&(Tl(ce)||ce[Ml]))break e;if((j||L)&&(L=G.window===G?G:(L=G.ownerDocument)?L.defaultView||L.parentWindow:window,j?(ce=n.relatedTarget||n.toElement,j=U,ce=ce?Tl(ce):null,ce!==null&&(Ne=f(ce),ie=ce.tag,ce!==Ne||ie!==5&&ie!==27&&ie!==6)&&(ce=null)):(j=null,ce=U),j!==ce)){if(ie=Tf,q="onMouseLeave",O="onMouseEnter",M="mouse",(e==="pointerout"||e==="pointerover")&&(ie=Of,q="onPointerLeave",O="onPointerEnter",M="pointer"),Ne=j==null?L:Ca(j),D=ce==null?L:Ca(ce),L=new ie(q,M+"leave",j,n,G),L.target=Ne,L.relatedTarget=D,q=null,Tl(G)===U&&(ie=new ie(O,M+"enter",ce,n,G),ie.target=D,ie.relatedTarget=Ne,q=ie),Ne=q,j&&ce)t:{for(ie=j,O=ce,M=0,D=ie;D;D=ia(D))M++;for(D=0,q=O;q;q=ia(q))D++;for(;0<M-D;)ie=ia(ie),M--;for(;0<D-M;)O=ia(O),D--;for(;M--;){if(ie===O||O!==null&&ie===O.alternate)break t;ie=ia(ie),O=ia(O)}ie=null}else ie=null;j!==null&&yh(Q,L,j,ie,!1),ce!==null&&Ne!==null&&yh(Q,Ne,ce,ie,!0)}}e:{if(L=U?Ca(U):window,j=L.nodeName&&L.nodeName.toLowerCase(),j==="select"||j==="input"&&L.type==="file")var ee=Hf;else if(Lf(L))if(Bf)ee=g0;else{ee=p0;var ye=h0}else j=L.nodeName,!j||j.toLowerCase()!=="input"||L.type!=="checkbox"&&L.type!=="radio"?U&&Qi(U.elementType)&&(ee=Hf):ee=v0;if(ee&&(ee=ee(e,U))){jf(Q,ee,n,G);break e}ye&&ye(e,L,U),e==="focusout"&&U&&L.type==="number"&&U.memoizedProps.value!=null&&Xi(L,"number",L.value)}switch(ye=U?Ca(U):window,e){case"focusin":(Lf(ye)||ye.contentEditable==="true")&&(Hl=ye,ro=U,Ha=null);break;case"focusout":Ha=ro=Hl=null;break;case"mousedown":uo=!0;break;case"contextmenu":case"mouseup":case"dragend":uo=!1,Zf(Q,n,G);break;case"selectionchange":if(b0)break;case"keydown":case"keyup":Zf(Q,n,G)}var ae;if(to)e:{switch(e){case"compositionstart":var oe="onCompositionStart";break e;case"compositionend":oe="onCompositionEnd";break e;case"compositionupdate":oe="onCompositionUpdate";break e}oe=void 0}else jl?zf(e,n)&&(oe="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(oe="onCompositionStart");oe&&(_f&&n.locale!=="ko"&&(jl||oe!=="onCompositionStart"?oe==="onCompositionEnd"&&jl&&(ae=Af()):(Cn=G,Ji="value"in Cn?Cn.value:Cn.textContent,jl=!0)),ye=Gu(U,oe),0<ye.length&&(oe=new Cf(oe,e,null,n,G),Q.push({event:oe,listeners:ye}),ae?oe.data=ae:(ae=Uf(n),ae!==null&&(oe.data=ae)))),(ae=c0?s0(e,n):f0(e,n))&&(oe=Gu(U,"onBeforeInput"),0<oe.length&&(ye=new Cf("onBeforeInput","beforeinput",null,n,G),Q.push({event:ye,listeners:oe}),ye.data=ae)),tb(Q,e,U,n,G)}vh(Q,t)})}function fr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gu(e,t){for(var n=t+"Capture",a=[];e!==null;){var r=e,c=r.stateNode;if(r=r.tag,r!==5&&r!==26&&r!==27||c===null||(r=Oa(e,n),r!=null&&a.unshift(fr(e,r,c)),r=Oa(e,t),r!=null&&a.push(fr(e,r,c))),e.tag===3)return a;e=e.return}return[]}function ia(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function yh(e,t,n,a,r){for(var c=t._reactName,d=[];n!==null&&n!==a;){var b=n,x=b.alternate,U=b.stateNode;if(b=b.tag,x!==null&&x===a)break;b!==5&&b!==26&&b!==27||U===null||(x=U,r?(U=Oa(n,c),U!=null&&d.unshift(fr(n,U,x))):r||(U=Oa(n,c),U!=null&&d.push(fr(n,U,x)))),n=n.return}d.length!==0&&e.push({event:t,listeners:d})}var rb=/\r\n?/g,ub=/\u0000|\uFFFD/g;function bh(e){return(typeof e=="string"?e:""+e).replace(rb,`
`).replace(ub,"")}function Sh(e,t){return t=bh(t),bh(e)===t}function Yu(){}function De(e,t,n,a,r,c){switch(n){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||zl(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&zl(e,""+a);break;case"className":Zr(e,"class",a);break;case"tabIndex":Zr(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Zr(e,n,a);break;case"style":Ef(e,a,c);break;case"data":if(t!=="object"){Zr(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=$r(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&De(e,t,"name",r.name,r,null),De(e,t,"formEncType",r.formEncType,r,null),De(e,t,"formMethod",r.formMethod,r,null),De(e,t,"formTarget",r.formTarget,r,null)):(De(e,t,"encType",r.encType,r,null),De(e,t,"method",r.method,r,null),De(e,t,"target",r.target,r,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=$r(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(e.onclick=Yu);break;case"onScroll":a!=null&&xe("scroll",e);break;case"onScrollEnd":a!=null&&xe("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(i(61));if(n=a.__html,n!=null){if(r.children!=null)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}n=$r(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":xe("beforetoggle",e),xe("toggle",e),Qr(e,"popover",a);break;case"xlinkActuate":nn(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":nn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":nn(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":nn(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":nn(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":nn(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":nn(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":nn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":nn(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Qr(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=jy.get(n)||n,Qr(e,n,a))}}function _c(e,t,n,a,r,c){switch(n){case"style":Ef(e,a,c);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(i(61));if(n=a.__html,n!=null){if(r.children!=null)throw Error(i(60));e.innerHTML=n}}break;case"children":typeof a=="string"?zl(e,a):(typeof a=="number"||typeof a=="bigint")&&zl(e,""+a);break;case"onScroll":a!=null&&xe("scroll",e);break;case"onScrollEnd":a!=null&&xe("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Yu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ff.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(r=n.endsWith("Capture"),t=n.slice(2,r?n.length-7:void 0),c=e[ot]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,r),typeof a=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,r);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):Qr(e,n,a)}}}function Ie(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":xe("error",e),xe("load",e);var a=!1,r=!1,c;for(c in n)if(n.hasOwnProperty(c)){var d=n[c];if(d!=null)switch(c){case"src":a=!0;break;case"srcSet":r=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:De(e,t,c,d,n,null)}}r&&De(e,t,"srcSet",n.srcSet,n,null),a&&De(e,t,"src",n.src,n,null);return;case"input":xe("invalid",e);var b=c=d=r=null,x=null,U=null;for(a in n)if(n.hasOwnProperty(a)){var G=n[a];if(G!=null)switch(a){case"name":r=G;break;case"type":d=G;break;case"checked":x=G;break;case"defaultChecked":U=G;break;case"value":c=G;break;case"defaultValue":b=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(i(137,t));break;default:De(e,t,a,G,n,null)}}yf(e,c,b,x,U,d,r,!1),Kr(e);return;case"select":xe("invalid",e),a=d=c=null;for(r in n)if(n.hasOwnProperty(r)&&(b=n[r],b!=null))switch(r){case"value":c=b;break;case"defaultValue":d=b;break;case"multiple":a=b;default:De(e,t,r,b,n,null)}t=c,n=d,e.multiple=!!a,t!=null?Nl(e,!!a,t,!1):n!=null&&Nl(e,!!a,n,!0);return;case"textarea":xe("invalid",e),c=r=a=null;for(d in n)if(n.hasOwnProperty(d)&&(b=n[d],b!=null))switch(d){case"value":a=b;break;case"defaultValue":r=b;break;case"children":c=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(i(91));break;default:De(e,t,d,b,n,null)}Sf(e,a,r,c),Kr(e);return;case"option":for(x in n)if(n.hasOwnProperty(x)&&(a=n[x],a!=null))switch(x){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:De(e,t,x,a,n,null)}return;case"dialog":xe("beforetoggle",e),xe("toggle",e),xe("cancel",e),xe("close",e);break;case"iframe":case"object":xe("load",e);break;case"video":case"audio":for(a=0;a<sr.length;a++)xe(sr[a],e);break;case"image":xe("error",e),xe("load",e);break;case"details":xe("toggle",e);break;case"embed":case"source":case"link":xe("error",e),xe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(U in n)if(n.hasOwnProperty(U)&&(a=n[U],a!=null))switch(U){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:De(e,t,U,a,n,null)}return;default:if(Qi(t)){for(G in n)n.hasOwnProperty(G)&&(a=n[G],a!==void 0&&_c(e,t,G,a,n,void 0));return}}for(b in n)n.hasOwnProperty(b)&&(a=n[b],a!=null&&De(e,t,b,a,n,null))}function ib(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,c=null,d=null,b=null,x=null,U=null,G=null;for(j in n){var Q=n[j];if(n.hasOwnProperty(j)&&Q!=null)switch(j){case"checked":break;case"value":break;case"defaultValue":x=Q;default:a.hasOwnProperty(j)||De(e,t,j,null,a,Q)}}for(var L in a){var j=a[L];if(Q=n[L],a.hasOwnProperty(L)&&(j!=null||Q!=null))switch(L){case"type":c=j;break;case"name":r=j;break;case"checked":U=j;break;case"defaultChecked":G=j;break;case"value":d=j;break;case"defaultValue":b=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(i(137,t));break;default:j!==Q&&De(e,t,L,j,a,Q)}}Vi(e,d,b,x,U,G,c,r);return;case"select":j=d=b=L=null;for(c in n)if(x=n[c],n.hasOwnProperty(c)&&x!=null)switch(c){case"value":break;case"multiple":j=x;default:a.hasOwnProperty(c)||De(e,t,c,null,a,x)}for(r in a)if(c=a[r],x=n[r],a.hasOwnProperty(r)&&(c!=null||x!=null))switch(r){case"value":L=c;break;case"defaultValue":b=c;break;case"multiple":d=c;default:c!==x&&De(e,t,r,c,a,x)}t=b,n=d,a=j,L!=null?Nl(e,!!n,L,!1):!!a!=!!n&&(t!=null?Nl(e,!!n,t,!0):Nl(e,!!n,n?[]:"",!1));return;case"textarea":j=L=null;for(b in n)if(r=n[b],n.hasOwnProperty(b)&&r!=null&&!a.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:De(e,t,b,null,a,r)}for(d in a)if(r=a[d],c=n[d],a.hasOwnProperty(d)&&(r!=null||c!=null))switch(d){case"value":L=r;break;case"defaultValue":j=r;break;case"children":break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(i(91));break;default:r!==c&&De(e,t,d,r,a,c)}bf(e,L,j);return;case"option":for(var ce in n)if(L=n[ce],n.hasOwnProperty(ce)&&L!=null&&!a.hasOwnProperty(ce))switch(ce){case"selected":e.selected=!1;break;default:De(e,t,ce,null,a,L)}for(x in a)if(L=a[x],j=n[x],a.hasOwnProperty(x)&&L!==j&&(L!=null||j!=null))switch(x){case"selected":e.selected=L&&typeof L!="function"&&typeof L!="symbol";break;default:De(e,t,x,L,a,j)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ie in n)L=n[ie],n.hasOwnProperty(ie)&&L!=null&&!a.hasOwnProperty(ie)&&De(e,t,ie,null,a,L);for(U in a)if(L=a[U],j=n[U],a.hasOwnProperty(U)&&L!==j&&(L!=null||j!=null))switch(U){case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(i(137,t));break;default:De(e,t,U,L,a,j)}return;default:if(Qi(t)){for(var Ne in n)L=n[Ne],n.hasOwnProperty(Ne)&&L!==void 0&&!a.hasOwnProperty(Ne)&&_c(e,t,Ne,void 0,a,L);for(G in a)L=a[G],j=n[G],!a.hasOwnProperty(G)||L===j||L===void 0&&j===void 0||_c(e,t,G,L,a,j);return}}for(var O in n)L=n[O],n.hasOwnProperty(O)&&L!=null&&!a.hasOwnProperty(O)&&De(e,t,O,null,a,L);for(Q in a)L=a[Q],j=n[Q],!a.hasOwnProperty(Q)||L===j||L==null&&j==null||De(e,t,Q,L,a,j)}var Dc=null,Nc=null;function qu(e){return e.nodeType===9?e:e.ownerDocument}function xh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Eh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function zc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Uc=null;function ob(){var e=window.event;return e&&e.type==="popstate"?e===Uc?!1:(Uc=e,!0):(Uc=null,!1)}var wh=typeof setTimeout=="function"?setTimeout:void 0,cb=typeof clearTimeout=="function"?clearTimeout:void 0,Rh=typeof Promise=="function"?Promise:void 0,sb=typeof queueMicrotask=="function"?queueMicrotask:typeof Rh<"u"?function(e){return Rh.resolve(null).then(e).catch(fb)}:wh;function fb(e){setTimeout(function(){throw e})}function Xn(e){return e==="head"}function Ah(e,t){var n=t,a=0,r=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<a&&8>a){n=a;var d=e.ownerDocument;if(n&1&&dr(d.documentElement),n&2&&dr(d.body),n&4)for(n=d.head,dr(n),d=n.firstChild;d;){var b=d.nextSibling,x=d.nodeName;d[Ta]||x==="SCRIPT"||x==="STYLE"||x==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=b}}if(r===0){e.removeChild(c),Sr(t);return}r--}else n==="$"||n==="$?"||n==="$!"?r++:a=n.charCodeAt(0)-48;else a=0;n=c}while(n);Sr(t)}function Lc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Lc(n),ki(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function db(e,t,n,a){for(;e.nodeType===1;){var r=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ta])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==r.rel||e.getAttribute("href")!==(r.href==null||r.href===""?null:r.href)||e.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin)||e.getAttribute("title")!==(r.title==null?null:r.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(r.src==null?null:r.src)||e.getAttribute("type")!==(r.type==null?null:r.type)||e.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=r.name==null?null:""+r.name;if(r.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Ht(e.nextSibling),e===null)break}return null}function mb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Ht(e.nextSibling),e===null))return null;return e}function jc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function hb(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Ht(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Hc=null;function Mh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Th(e,t,n){switch(t=qu(n),e){case"html":if(e=t.documentElement,!e)throw Error(i(452));return e;case"head":if(e=t.head,!e)throw Error(i(453));return e;case"body":if(e=t.body,!e)throw Error(i(454));return e;default:throw Error(i(451))}}function dr(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);ki(e)}var zt=new Map,Ch=new Set;function Vu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var gn=K.d;K.d={f:pb,r:vb,D:gb,C:yb,L:bb,m:Sb,X:Eb,S:xb,M:wb};function pb(){var e=gn.f(),t=Uu();return e||t}function vb(e){var t=Cl(e);t!==null&&t.tag===5&&t.type==="form"?Pd(t):gn.r(e)}var oa=typeof document>"u"?null:document;function Oh(e,t,n){var a=oa;if(a&&typeof t=="string"&&t){var r=Mt(t);r='link[rel="'+e+'"][href="'+r+'"]',typeof n=="string"&&(r+='[crossorigin="'+n+'"]'),Ch.has(r)||(Ch.add(r),e={rel:e,crossOrigin:n,href:t},a.querySelector(r)===null&&(t=a.createElement("link"),Ie(t,"link",e),Ke(t),a.head.appendChild(t)))}}function gb(e){gn.D(e),Oh("dns-prefetch",e,null)}function yb(e,t){gn.C(e,t),Oh("preconnect",e,t)}function bb(e,t,n){gn.L(e,t,n);var a=oa;if(a&&e&&t){var r='link[rel="preload"][as="'+Mt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(r+='[imagesrcset="'+Mt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(r+='[imagesizes="'+Mt(n.imageSizes)+'"]')):r+='[href="'+Mt(e)+'"]';var c=r;switch(t){case"style":c=ca(e);break;case"script":c=sa(e)}zt.has(c)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),zt.set(c,e),a.querySelector(r)!==null||t==="style"&&a.querySelector(mr(c))||t==="script"&&a.querySelector(hr(c))||(t=a.createElement("link"),Ie(t,"link",e),Ke(t),a.head.appendChild(t)))}}function Sb(e,t){gn.m(e,t);var n=oa;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",r='link[rel="modulepreload"][as="'+Mt(a)+'"][href="'+Mt(e)+'"]',c=r;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=sa(e)}if(!zt.has(c)&&(e=y({rel:"modulepreload",href:e},t),zt.set(c,e),n.querySelector(r)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(hr(c)))return}a=n.createElement("link"),Ie(a,"link",e),Ke(a),n.head.appendChild(a)}}}function xb(e,t,n){gn.S(e,t,n);var a=oa;if(a&&e){var r=Ol(a).hoistableStyles,c=ca(e);t=t||"default";var d=r.get(c);if(!d){var b={loading:0,preload:null};if(d=a.querySelector(mr(c)))b.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=zt.get(c))&&Bc(e,n);var x=d=a.createElement("link");Ke(x),Ie(x,"link",e),x._p=new Promise(function(U,G){x.onload=U,x.onerror=G}),x.addEventListener("load",function(){b.loading|=1}),x.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Xu(d,t,a)}d={type:"stylesheet",instance:d,count:1,state:b},r.set(c,d)}}}function Eb(e,t){gn.X(e,t);var n=oa;if(n&&e){var a=Ol(n).hoistableScripts,r=sa(e),c=a.get(r);c||(c=n.querySelector(hr(r)),c||(e=y({src:e,async:!0},t),(t=zt.get(r))&&kc(e,t),c=n.createElement("script"),Ke(c),Ie(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(r,c))}}function wb(e,t){gn.M(e,t);var n=oa;if(n&&e){var a=Ol(n).hoistableScripts,r=sa(e),c=a.get(r);c||(c=n.querySelector(hr(r)),c||(e=y({src:e,async:!0,type:"module"},t),(t=zt.get(r))&&kc(e,t),c=n.createElement("script"),Ke(c),Ie(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(r,c))}}function _h(e,t,n,a){var r=(r=re.current)?Vu(r):null;if(!r)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ca(n.href),n=Ol(r).hoistableStyles,a=n.get(t),a||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ca(n.href);var c=Ol(r).hoistableStyles,d=c.get(e);if(d||(r=r.ownerDocument||r,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=r.querySelector(mr(e)))&&!c._p&&(d.instance=c,d.state.loading=5),zt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},zt.set(e,n),c||Rb(r,e,n,d.state))),t&&a===null)throw Error(i(528,""));return d}if(t&&a!==null)throw Error(i(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=sa(n),n=Ol(r).hoistableScripts,a=n.get(t),a||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function ca(e){return'href="'+Mt(e)+'"'}function mr(e){return'link[rel="stylesheet"]['+e+"]"}function Dh(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function Rb(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Ie(t,"link",n),Ke(t),e.head.appendChild(t))}function sa(e){return'[src="'+Mt(e)+'"]'}function hr(e){return"script[async]"+e}function Nh(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Mt(n.href)+'"]');if(a)return t.instance=a,Ke(a),a;var r=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Ke(a),Ie(a,"style",r),Xu(a,n.precedence,e),t.instance=a;case"stylesheet":r=ca(n.href);var c=e.querySelector(mr(r));if(c)return t.state.loading|=4,t.instance=c,Ke(c),c;a=Dh(n),(r=zt.get(r))&&Bc(a,r),c=(e.ownerDocument||e).createElement("link"),Ke(c);var d=c;return d._p=new Promise(function(b,x){d.onload=b,d.onerror=x}),Ie(c,"link",a),t.state.loading|=4,Xu(c,n.precedence,e),t.instance=c;case"script":return c=sa(n.src),(r=e.querySelector(hr(c)))?(t.instance=r,Ke(r),r):(a=n,(r=zt.get(c))&&(a=y({},n),kc(a,r)),e=e.ownerDocument||e,r=e.createElement("script"),Ke(r),Ie(r,"link",a),e.head.appendChild(r),t.instance=r);case"void":return null;default:throw Error(i(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Xu(a,n.precedence,e));return t.instance}function Xu(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=a.length?a[a.length-1]:null,c=r,d=0;d<a.length;d++){var b=a[d];if(b.dataset.precedence===t)c=b;else if(c!==r)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Bc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function kc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Qu=null;function zh(e,t,n){if(Qu===null){var a=new Map,r=Qu=new Map;r.set(n,a)}else r=Qu,a=r.get(n),a||(a=new Map,r.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),r=0;r<n.length;r++){var c=n[r];if(!(c[Ta]||c[tt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var d=c.getAttribute(t)||"";d=e+d;var b=a.get(d);b?b.push(c):a.set(d,[c])}}return a}function Uh(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function Ab(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Lh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var pr=null;function Mb(){}function Tb(e,t,n){if(pr===null)throw Error(i(475));var a=pr;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var r=ca(n.href),c=e.querySelector(mr(r));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Zu.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=c,Ke(c);return}c=e.ownerDocument||e,n=Dh(n),(r=zt.get(r))&&Bc(n,r),c=c.createElement("link"),Ke(c);var d=c;d._p=new Promise(function(b,x){d.onload=b,d.onerror=x}),Ie(c,"link",n),t.instance=c}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Zu.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Cb(){if(pr===null)throw Error(i(475));var e=pr;return e.stylesheets&&e.count===0&&Gc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Gc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Zu(){if(this.count--,this.count===0){if(this.stylesheets)Gc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ku=null;function Gc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ku=new Map,t.forEach(Ob,e),Ku=null,Zu.call(e))}function Ob(e,t){if(!(t.state.loading&4)){var n=Ku.get(e);if(n)var a=n.get(null);else{n=new Map,Ku.set(e,n);for(var r=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<r.length;c++){var d=r[c];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(n.set(d.dataset.precedence,d),a=d)}a&&n.set(null,a)}r=t.instance,d=r.getAttribute("data-precedence"),c=n.get(d)||a,c===a&&n.set(null,r),n.set(d,r),this.count++,a=Zu.bind(this),r.addEventListener("load",a),r.addEventListener("error",a),c?c.parentNode.insertBefore(r,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(r,e.firstChild)),t.state.loading|=4}}var vr={$$typeof:k,Provider:null,Consumer:null,_currentValue:B,_currentValue2:B,_threadCount:0};function _b(e,t,n,a,r,c,d,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Li(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Li(0),this.hiddenUpdates=Li(null),this.identifierPrefix=a,this.onUncaughtError=r,this.onCaughtError=c,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function jh(e,t,n,a,r,c,d,b,x,U,G,Q){return e=new _b(e,t,n,d,b,x,U,Q),t=1,c===!0&&(t|=24),c=gt(3,null,null,t),e.current=c,c.stateNode=e,t=xo(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:a,isDehydrated:n,cache:t},Ao(c),e}function Hh(e){return e?(e=Yl,e):Yl}function Bh(e,t,n,a,r,c){r=Hh(r),a.context===null?a.context=r:a.pendingContext=r,a=Dn(t),a.payload={element:n},c=c===void 0?null:c,c!==null&&(a.callback=c),n=Nn(e,a,t),n!==null&&(Et(n,e,t),Za(n,e,t))}function kh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Yc(e,t){kh(e,t),(e=e.alternate)&&kh(e,t)}function Gh(e){if(e.tag===13){var t=Gl(e,67108864);t!==null&&Et(t,e,67108864),Yc(e,67108864)}}var Pu=!0;function Db(e,t,n,a){var r=z.T;z.T=null;var c=K.p;try{K.p=2,qc(e,t,n,a)}finally{K.p=c,z.T=r}}function Nb(e,t,n,a){var r=z.T;z.T=null;var c=K.p;try{K.p=8,qc(e,t,n,a)}finally{K.p=c,z.T=r}}function qc(e,t,n,a){if(Pu){var r=Vc(a);if(r===null)Oc(e,t,a,$u,n),qh(e,a);else if(Ub(r,e,t,n,a))a.stopPropagation();else if(qh(e,a),t&4&&-1<zb.indexOf(e)){for(;r!==null;){var c=Cl(r);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var d=nl(c.pendingLanes);if(d!==0){var b=c;for(b.pendingLanes|=2,b.entangledLanes|=2;d;){var x=1<<31-pt(d);b.entanglements[1]|=x,d&=~x}Pt(c),(Ce&6)===0&&(Nu=Vt()+500,cr(0))}}break;case 13:b=Gl(c,2),b!==null&&Et(b,c,2),Uu(),Yc(c,2)}if(c=Vc(a),c===null&&Oc(e,t,a,$u,n),c===r)break;r=c}r!==null&&a.stopPropagation()}else Oc(e,t,a,null,n)}}function Vc(e){return e=Ki(e),Xc(e)}var $u=null;function Xc(e){if($u=null,e=Tl(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=m(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return $u=e,null}function Yh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(yy()){case ef:return 2;case tf:return 8;case Yr:case by:return 32;case nf:return 268435456;default:return 32}default:return 32}}var Qc=!1,Qn=null,Zn=null,Kn=null,gr=new Map,yr=new Map,Pn=[],zb="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function qh(e,t){switch(e){case"focusin":case"focusout":Qn=null;break;case"dragenter":case"dragleave":Zn=null;break;case"mouseover":case"mouseout":Kn=null;break;case"pointerover":case"pointerout":gr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":yr.delete(t.pointerId)}}function br(e,t,n,a,r,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:c,targetContainers:[r]},t!==null&&(t=Cl(t),t!==null&&Gh(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,r!==null&&t.indexOf(r)===-1&&t.push(r),e)}function Ub(e,t,n,a,r){switch(t){case"focusin":return Qn=br(Qn,e,t,n,a,r),!0;case"dragenter":return Zn=br(Zn,e,t,n,a,r),!0;case"mouseover":return Kn=br(Kn,e,t,n,a,r),!0;case"pointerover":var c=r.pointerId;return gr.set(c,br(gr.get(c)||null,e,t,n,a,r)),!0;case"gotpointercapture":return c=r.pointerId,yr.set(c,br(yr.get(c)||null,e,t,n,a,r)),!0}return!1}function Vh(e){var t=Tl(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=m(n),t!==null){e.blockedOn=t,Ty(e.priority,function(){if(n.tag===13){var a=xt();a=ji(a);var r=Gl(n,a);r!==null&&Et(r,n,a),Yc(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ju(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Vc(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n);Zi=a,n.target.dispatchEvent(a),Zi=null}else return t=Cl(n),t!==null&&Gh(t),e.blockedOn=n,!1;t.shift()}return!0}function Xh(e,t,n){Ju(e)&&n.delete(t)}function Lb(){Qc=!1,Qn!==null&&Ju(Qn)&&(Qn=null),Zn!==null&&Ju(Zn)&&(Zn=null),Kn!==null&&Ju(Kn)&&(Kn=null),gr.forEach(Xh),yr.forEach(Xh)}function Fu(e,t){e.blockedOn===t&&(e.blockedOn=null,Qc||(Qc=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,Lb)))}var Wu=null;function Qh(e){Wu!==e&&(Wu=e,l.unstable_scheduleCallback(l.unstable_NormalPriority,function(){Wu===e&&(Wu=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],r=e[t+2];if(typeof a!="function"){if(Xc(a||n)===null)continue;break}var c=Cl(n);c!==null&&(e.splice(t,3),t-=3,Xo(c,{pending:!0,data:r,method:n.method,action:a},a,r))}}))}function Sr(e){function t(x){return Fu(x,e)}Qn!==null&&Fu(Qn,e),Zn!==null&&Fu(Zn,e),Kn!==null&&Fu(Kn,e),gr.forEach(t),yr.forEach(t);for(var n=0;n<Pn.length;n++){var a=Pn[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Pn.length&&(n=Pn[0],n.blockedOn===null);)Vh(n),n.blockedOn===null&&Pn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var r=n[a],c=n[a+1],d=r[ot]||null;if(typeof c=="function")d||Qh(n);else if(d){var b=null;if(c&&c.hasAttribute("formAction")){if(r=c,d=c[ot]||null)b=d.formAction;else if(Xc(r)!==null)continue}else b=d.action;typeof b=="function"?n[a+1]=b:(n.splice(a,3),a-=3),Qh(n)}}}function Zc(e){this._internalRoot=e}Iu.prototype.render=Zc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));var n=t.current,a=xt();Bh(n,a,e,t,null,null)},Iu.prototype.unmount=Zc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Bh(e.current,2,null,e,null,null),Uu(),t[Ml]=null}};function Iu(e){this._internalRoot=e}Iu.prototype.unstable_scheduleHydration=function(e){if(e){var t=of();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pn.length&&t!==0&&t<Pn[n].priority;n++);Pn.splice(n,0,e),n===0&&Vh(e)}};var Zh=u.version;if(Zh!=="19.1.1")throw Error(i(527,Zh,"19.1.1"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=v(t),e=e!==null?h(e):null,e=e===null?null:e.stateNode,e};var jb={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ei=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ei.isDisabled&&ei.supportsFiber)try{Ra=ei.inject(jb),ht=ei}catch{}}return Er.createRoot=function(e,t){if(!s(e))throw Error(i(299));var n=!1,a="",r=om,c=cm,d=sm,b=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(r=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(b=t.unstable_transitionCallbacks)),t=jh(e,1,!1,null,null,n,a,r,c,d,b,null),e[Ml]=t.current,Cc(e),new Zc(t)},Er.hydrateRoot=function(e,t,n){if(!s(e))throw Error(i(299));var a=!1,r="",c=om,d=cm,b=sm,x=null,U=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(x=n.unstable_transitionCallbacks),n.formState!==void 0&&(U=n.formState)),t=jh(e,1,!0,t,n??null,a,r,c,d,b,x,U),t.context=Hh(null),n=t.current,a=xt(),a=ji(a),r=Dn(a),r.callback=null,Nn(n,r,a),n=a,t.current.lanes=n,Ma(t,n),Pt(t),e[Ml]=t.current,Cc(e),new Iu(t)},Er.version="19.1.1",Er}var np;function Zb(){if(np)return $c.exports;np=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(u){console.error(u)}}return l(),$c.exports=Qb(),$c.exports}var Kb=Zb();const Pb={theme:"system",setTheme:()=>null},Xp=g.createContext(Pb);function $b({children:l,defaultTheme:u="system",storageKey:o="vite-ui-theme",...i}){const[s,f]=g.useState(()=>localStorage.getItem(o)||u);g.useEffect(()=>{const p=window.document.documentElement;if(p.classList.remove("light","dark"),s==="system"){const v=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";p.classList.add(v);return}p.classList.add(s)},[s]);const m={theme:s,setTheme:p=>{localStorage.setItem(o,p),f(p)}};return _.jsx(Xp.Provider,{...i,value:m,children:l})}const Jb=()=>{const l=g.useContext(Xp);if(l===void 0)throw new Error("useTheme must be used within a ThemeProvider");return l};/**
 * react-router v7.9.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var lp="popstate";function Fb(l={}){function u(i,s){let{pathname:f,search:m,hash:p}=i.location;return ms("",{pathname:f,search:m,hash:p},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function o(i,s){return typeof s=="string"?s:Mr(s)}return Ib(u,o,null,l)}function He(l,u){if(l===!1||l===null||typeof l>"u")throw new Error(u)}function Wt(l,u){if(!l){typeof console<"u"&&console.warn(u);try{throw new Error(u)}catch{}}}function Wb(){return Math.random().toString(36).substring(2,10)}function ap(l,u){return{usr:l.state,key:l.key,idx:u}}function ms(l,u,o=null,i){return{pathname:typeof l=="string"?l:l.pathname,search:"",hash:"",...typeof u=="string"?ya(u):u,state:o,key:u&&u.key||i||Wb()}}function Mr({pathname:l="/",search:u="",hash:o=""}){return u&&u!=="?"&&(l+=u.charAt(0)==="?"?u:"?"+u),o&&o!=="#"&&(l+=o.charAt(0)==="#"?o:"#"+o),l}function ya(l){let u={};if(l){let o=l.indexOf("#");o>=0&&(u.hash=l.substring(o),l=l.substring(0,o));let i=l.indexOf("?");i>=0&&(u.search=l.substring(i),l=l.substring(0,i)),l&&(u.pathname=l)}return u}function Ib(l,u,o,i={}){let{window:s=document.defaultView,v5Compat:f=!1}=i,m=s.history,p="POP",v=null,h=y();h==null&&(h=0,m.replaceState({...m.state,idx:h},""));function y(){return(m.state||{idx:null}).idx}function S(){p="POP";let T=y(),N=T==null?null:T-h;h=T,v&&v({action:p,location:R.location,delta:N})}function E(T,N){p="PUSH";let H=ms(R.location,T,N);h=y()+1;let k=ap(H,h),X=R.createHref(H);try{m.pushState(k,"",X)}catch(Y){if(Y instanceof DOMException&&Y.name==="DataCloneError")throw Y;s.location.assign(X)}f&&v&&v({action:p,location:R.location,delta:1})}function A(T,N){p="REPLACE";let H=ms(R.location,T,N);h=y();let k=ap(H,h),X=R.createHref(H);m.replaceState(k,"",X),f&&v&&v({action:p,location:R.location,delta:0})}function C(T){return eS(T)}let R={get action(){return p},get location(){return l(s,m)},listen(T){if(v)throw new Error("A history only accepts one active listener");return s.addEventListener(lp,S),v=T,()=>{s.removeEventListener(lp,S),v=null}},createHref(T){return u(s,T)},createURL:C,encodeLocation(T){let N=C(T);return{pathname:N.pathname,search:N.search,hash:N.hash}},push:E,replace:A,go(T){return m.go(T)}};return R}function eS(l,u=!1){let o="http://localhost";typeof window<"u"&&(o=window.location.origin!=="null"?window.location.origin:window.location.href),He(o,"No window.location.(origin|href) available to create URL");let i=typeof l=="string"?l:Mr(l);return i=i.replace(/ $/,"%20"),!u&&i.startsWith("//")&&(i=o+i),new URL(i,o)}function Qp(l,u,o="/"){return tS(l,u,o,!1)}function tS(l,u,o,i){let s=typeof u=="string"?ya(u):u,f=Sn(s.pathname||"/",o);if(f==null)return null;let m=Zp(l);nS(m);let p=null;for(let v=0;p==null&&v<m.length;++v){let h=mS(f);p=fS(m[v],h,i)}return p}function Zp(l,u=[],o=[],i="",s=!1){let f=(m,p,v=s,h)=>{let y={relativePath:h===void 0?m.path||"":h,caseSensitive:m.caseSensitive===!0,childrenIndex:p,route:m};if(y.relativePath.startsWith("/")){if(!y.relativePath.startsWith(i)&&v)return;He(y.relativePath.startsWith(i),`Absolute route path "${y.relativePath}" nested under path "${i}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),y.relativePath=y.relativePath.slice(i.length)}let S=bn([i,y.relativePath]),E=o.concat(y);m.children&&m.children.length>0&&(He(m.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${S}".`),Zp(m.children,u,E,S,v)),!(m.path==null&&!m.index)&&u.push({path:S,score:cS(S,m.index),routesMeta:E})};return l.forEach((m,p)=>{if(m.path===""||!m.path?.includes("?"))f(m,p);else for(let v of Kp(m.path))f(m,p,!0,v)}),u}function Kp(l){let u=l.split("/");if(u.length===0)return[];let[o,...i]=u,s=o.endsWith("?"),f=o.replace(/\?$/,"");if(i.length===0)return s?[f,""]:[f];let m=Kp(i.join("/")),p=[];return p.push(...m.map(v=>v===""?f:[f,v].join("/"))),s&&p.push(...m),p.map(v=>l.startsWith("/")&&v===""?"/":v)}function nS(l){l.sort((u,o)=>u.score!==o.score?o.score-u.score:sS(u.routesMeta.map(i=>i.childrenIndex),o.routesMeta.map(i=>i.childrenIndex)))}var lS=/^:[\w-]+$/,aS=3,rS=2,uS=1,iS=10,oS=-2,rp=l=>l==="*";function cS(l,u){let o=l.split("/"),i=o.length;return o.some(rp)&&(i+=oS),u&&(i+=rS),o.filter(s=>!rp(s)).reduce((s,f)=>s+(lS.test(f)?aS:f===""?uS:iS),i)}function sS(l,u){return l.length===u.length&&l.slice(0,-1).every((i,s)=>i===u[s])?l[l.length-1]-u[u.length-1]:0}function fS(l,u,o=!1){let{routesMeta:i}=l,s={},f="/",m=[];for(let p=0;p<i.length;++p){let v=i[p],h=p===i.length-1,y=f==="/"?u:u.slice(f.length)||"/",S=vi({path:v.relativePath,caseSensitive:v.caseSensitive,end:h},y),E=v.route;if(!S&&h&&o&&!i[i.length-1].route.index&&(S=vi({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},y)),!S)return null;Object.assign(s,S.params),m.push({params:s,pathname:bn([f,S.pathname]),pathnameBase:gS(bn([f,S.pathnameBase])),route:E}),S.pathnameBase!=="/"&&(f=bn([f,S.pathnameBase]))}return m}function vi(l,u){typeof l=="string"&&(l={path:l,caseSensitive:!1,end:!0});let[o,i]=dS(l.path,l.caseSensitive,l.end),s=u.match(o);if(!s)return null;let f=s[0],m=f.replace(/(.)\/+$/,"$1"),p=s.slice(1);return{params:i.reduce((h,{paramName:y,isOptional:S},E)=>{if(y==="*"){let C=p[E]||"";m=f.slice(0,f.length-C.length).replace(/(.)\/+$/,"$1")}const A=p[E];return S&&!A?h[y]=void 0:h[y]=(A||"").replace(/%2F/g,"/"),h},{}),pathname:f,pathnameBase:m,pattern:l}}function dS(l,u=!1,o=!0){Wt(l==="*"||!l.endsWith("*")||l.endsWith("/*"),`Route path "${l}" will be treated as if it were "${l.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${l.replace(/\*$/,"/*")}".`);let i=[],s="^"+l.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(m,p,v)=>(i.push({paramName:p,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)")).replace(/\/([\w-]+)\?(\/|$)/g,"(/$1)?$2");return l.endsWith("*")?(i.push({paramName:"*"}),s+=l==="*"||l==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?s+="\\/*$":l!==""&&l!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,u?void 0:"i"),i]}function mS(l){try{return l.split("/").map(u=>decodeURIComponent(u).replace(/\//g,"%2F")).join("/")}catch(u){return Wt(!1,`The URL path "${l}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${u}).`),l}}function Sn(l,u){if(u==="/")return l;if(!l.toLowerCase().startsWith(u.toLowerCase()))return null;let o=u.endsWith("/")?u.length-1:u.length,i=l.charAt(o);return i&&i!=="/"?null:l.slice(o)||"/"}function hS(l,u="/"){let{pathname:o,search:i="",hash:s=""}=typeof l=="string"?ya(l):l;return{pathname:o?o.startsWith("/")?o:pS(o,u):u,search:yS(i),hash:bS(s)}}function pS(l,u){let o=u.replace(/\/+$/,"").split("/");return l.split("/").forEach(s=>{s===".."?o.length>1&&o.pop():s!=="."&&o.push(s)}),o.length>1?o.join("/"):"/"}function Ic(l,u,o,i){return`Cannot include a '${l}' character in a manually specified \`to.${u}\` field [${JSON.stringify(i)}].  Please separate it out to the \`to.${o}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function vS(l){return l.filter((u,o)=>o===0||u.route.path&&u.route.path.length>0)}function Pp(l){let u=vS(l);return u.map((o,i)=>i===u.length-1?o.pathname:o.pathnameBase)}function $p(l,u,o,i=!1){let s;typeof l=="string"?s=ya(l):(s={...l},He(!s.pathname||!s.pathname.includes("?"),Ic("?","pathname","search",s)),He(!s.pathname||!s.pathname.includes("#"),Ic("#","pathname","hash",s)),He(!s.search||!s.search.includes("#"),Ic("#","search","hash",s)));let f=l===""||s.pathname==="",m=f?"/":s.pathname,p;if(m==null)p=o;else{let S=u.length-1;if(!i&&m.startsWith("..")){let E=m.split("/");for(;E[0]==="..";)E.shift(),S-=1;s.pathname=E.join("/")}p=S>=0?u[S]:"/"}let v=hS(s,p),h=m&&m!=="/"&&m.endsWith("/"),y=(f||m===".")&&o.endsWith("/");return!v.pathname.endsWith("/")&&(h||y)&&(v.pathname+="/"),v}var bn=l=>l.join("/").replace(/\/\/+/g,"/"),gS=l=>l.replace(/\/+$/,"").replace(/^\/*/,"/"),yS=l=>!l||l==="?"?"":l.startsWith("?")?l:"?"+l,bS=l=>!l||l==="#"?"":l.startsWith("#")?l:"#"+l;function SS(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.internal=="boolean"&&"data"in l}var Jp=["POST","PUT","PATCH","DELETE"];new Set(Jp);var xS=["GET",...Jp];new Set(xS);var ba=g.createContext(null);ba.displayName="DataRouter";var wi=g.createContext(null);wi.displayName="DataRouterState";g.createContext(!1);var Fp=g.createContext({isTransitioning:!1});Fp.displayName="ViewTransition";var ES=g.createContext(new Map);ES.displayName="Fetchers";var wS=g.createContext(null);wS.displayName="Await";var en=g.createContext(null);en.displayName="Navigation";var Nr=g.createContext(null);Nr.displayName="Location";var Rn=g.createContext({outlet:null,matches:[],isDataRoute:!1});Rn.displayName="Route";var Os=g.createContext(null);Os.displayName="RouteError";function RS(l,{relative:u}={}){He(zr(),"useHref() may be used only in the context of a <Router> component.");let{basename:o,navigator:i}=g.useContext(en),{hash:s,pathname:f,search:m}=Ur(l,{relative:u}),p=f;return o!=="/"&&(p=f==="/"?o:bn([o,f])),i.createHref({pathname:p,search:m,hash:s})}function zr(){return g.useContext(Nr)!=null}function El(){return He(zr(),"useLocation() may be used only in the context of a <Router> component."),g.useContext(Nr).location}var Wp="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Ip(l){g.useContext(en).static||g.useLayoutEffect(l)}function _s(){let{isDataRoute:l}=g.useContext(Rn);return l?HS():AS()}function AS(){He(zr(),"useNavigate() may be used only in the context of a <Router> component.");let l=g.useContext(ba),{basename:u,navigator:o}=g.useContext(en),{matches:i}=g.useContext(Rn),{pathname:s}=El(),f=JSON.stringify(Pp(i)),m=g.useRef(!1);return Ip(()=>{m.current=!0}),g.useCallback((v,h={})=>{if(Wt(m.current,Wp),!m.current)return;if(typeof v=="number"){o.go(v);return}let y=$p(v,JSON.parse(f),s,h.relative==="path");l==null&&u!=="/"&&(y.pathname=y.pathname==="/"?u:bn([u,y.pathname])),(h.replace?o.replace:o.push)(y,h.state,h)},[u,o,f,s,l])}g.createContext(null);function Ur(l,{relative:u}={}){let{matches:o}=g.useContext(Rn),{pathname:i}=El(),s=JSON.stringify(Pp(o));return g.useMemo(()=>$p(l,JSON.parse(s),i,u==="path"),[l,s,i,u])}function MS(l,u){return ev(l,u)}function ev(l,u,o,i,s){He(zr(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:f}=g.useContext(en),{matches:m}=g.useContext(Rn),p=m[m.length-1],v=p?p.params:{},h=p?p.pathname:"/",y=p?p.pathnameBase:"/",S=p&&p.route;{let H=S&&S.path||"";tv(h,!S||H.endsWith("*")||H.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${h}" (under <Route path="${H}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${H}"> to <Route path="${H==="/"?"*":`${H}/*`}">.`)}let E=El(),A;if(u){let H=typeof u=="string"?ya(u):u;He(y==="/"||H.pathname?.startsWith(y),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${y}" but pathname "${H.pathname}" was given in the \`location\` prop.`),A=H}else A=E;let C=A.pathname||"/",R=C;if(y!=="/"){let H=y.replace(/^\//,"").split("/");R="/"+C.replace(/^\//,"").split("/").slice(H.length).join("/")}let T=Qp(l,{pathname:R});Wt(S||T!=null,`No routes matched location "${A.pathname}${A.search}${A.hash}" `),Wt(T==null||T[T.length-1].route.element!==void 0||T[T.length-1].route.Component!==void 0||T[T.length-1].route.lazy!==void 0,`Matched leaf route at location "${A.pathname}${A.search}${A.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let N=DS(T&&T.map(H=>Object.assign({},H,{params:Object.assign({},v,H.params),pathname:bn([y,f.encodeLocation?f.encodeLocation(H.pathname).pathname:H.pathname]),pathnameBase:H.pathnameBase==="/"?y:bn([y,f.encodeLocation?f.encodeLocation(H.pathnameBase).pathname:H.pathnameBase])})),m,o,i,s);return u&&N?g.createElement(Nr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...A},navigationType:"POP"}},N):N}function TS(){let l=jS(),u=SS(l)?`${l.status} ${l.statusText}`:l instanceof Error?l.message:JSON.stringify(l),o=l instanceof Error?l.stack:null,i="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:i},f={padding:"2px 4px",backgroundColor:i},m=null;return console.error("Error handled by React Router default ErrorBoundary:",l),m=g.createElement(g.Fragment,null,g.createElement("p",null,"💿 Hey developer 👋"),g.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",g.createElement("code",{style:f},"ErrorBoundary")," or"," ",g.createElement("code",{style:f},"errorElement")," prop on your route.")),g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},u),o?g.createElement("pre",{style:s},o):null,m)}var CS=g.createElement(TS,null),OS=class extends g.Component{constructor(l){super(l),this.state={location:l.location,revalidation:l.revalidation,error:l.error}}static getDerivedStateFromError(l){return{error:l}}static getDerivedStateFromProps(l,u){return u.location!==l.location||u.revalidation!=="idle"&&l.revalidation==="idle"?{error:l.error,location:l.location,revalidation:l.revalidation}:{error:l.error!==void 0?l.error:u.error,location:u.location,revalidation:l.revalidation||u.revalidation}}componentDidCatch(l,u){this.props.unstable_onError?this.props.unstable_onError(l,u):console.error("React Router caught the following error during render",l)}render(){return this.state.error!==void 0?g.createElement(Rn.Provider,{value:this.props.routeContext},g.createElement(Os.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function _S({routeContext:l,match:u,children:o}){let i=g.useContext(ba);return i&&i.static&&i.staticContext&&(u.route.errorElement||u.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=u.route.id),g.createElement(Rn.Provider,{value:l},o)}function DS(l,u=[],o=null,i=null,s=null){if(l==null){if(!o)return null;if(o.errors)l=o.matches;else if(u.length===0&&!o.initialized&&o.matches.length>0)l=o.matches;else return null}let f=l,m=o?.errors;if(m!=null){let h=f.findIndex(y=>y.route.id&&m?.[y.route.id]!==void 0);He(h>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(m).join(",")}`),f=f.slice(0,Math.min(f.length,h+1))}let p=!1,v=-1;if(o)for(let h=0;h<f.length;h++){let y=f[h];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(v=h),y.route.id){let{loaderData:S,errors:E}=o,A=y.route.loader&&!S.hasOwnProperty(y.route.id)&&(!E||E[y.route.id]===void 0);if(y.route.lazy||A){p=!0,v>=0?f=f.slice(0,v+1):f=[f[0]];break}}}return f.reduceRight((h,y,S)=>{let E,A=!1,C=null,R=null;o&&(E=m&&y.route.id?m[y.route.id]:void 0,C=y.route.errorElement||CS,p&&(v<0&&S===0?(tv("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),A=!0,R=null):v===S&&(A=!0,R=y.route.hydrateFallbackElement||null)));let T=u.concat(f.slice(0,S+1)),N=()=>{let H;return E?H=C:A?H=R:y.route.Component?H=g.createElement(y.route.Component,null):y.route.element?H=y.route.element:H=h,g.createElement(_S,{match:y,routeContext:{outlet:h,matches:T,isDataRoute:o!=null},children:H})};return o&&(y.route.ErrorBoundary||y.route.errorElement||S===0)?g.createElement(OS,{location:o.location,revalidation:o.revalidation,component:C,error:E,children:N(),routeContext:{outlet:null,matches:T,isDataRoute:!0},unstable_onError:i}):N()},null)}function Ds(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function NS(l){let u=g.useContext(ba);return He(u,Ds(l)),u}function zS(l){let u=g.useContext(wi);return He(u,Ds(l)),u}function US(l){let u=g.useContext(Rn);return He(u,Ds(l)),u}function Ns(l){let u=US(l),o=u.matches[u.matches.length-1];return He(o.route.id,`${l} can only be used on routes that contain a unique "id"`),o.route.id}function LS(){return Ns("useRouteId")}function jS(){let l=g.useContext(Os),u=zS("useRouteError"),o=Ns("useRouteError");return l!==void 0?l:u.errors?.[o]}function HS(){let{router:l}=NS("useNavigate"),u=Ns("useNavigate"),o=g.useRef(!1);return Ip(()=>{o.current=!0}),g.useCallback(async(s,f={})=>{Wt(o.current,Wp),o.current&&(typeof s=="number"?l.navigate(s):await l.navigate(s,{fromRouteId:u,...f}))},[l,u])}var up={};function tv(l,u,o){!u&&!up[l]&&(up[l]=!0,Wt(!1,o))}g.memo(BS);function BS({routes:l,future:u,state:o,unstable_onError:i}){return ev(l,void 0,o,i,u)}function hs(l){He(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function kS({basename:l="/",children:u=null,location:o,navigationType:i="POP",navigator:s,static:f=!1}){He(!zr(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let m=l.replace(/^\/*/,"/"),p=g.useMemo(()=>({basename:m,navigator:s,static:f,future:{}}),[m,s,f]);typeof o=="string"&&(o=ya(o));let{pathname:v="/",search:h="",hash:y="",state:S=null,key:E="default"}=o,A=g.useMemo(()=>{let C=Sn(v,m);return C==null?null:{location:{pathname:C,search:h,hash:y,state:S,key:E},navigationType:i}},[m,v,h,y,S,E,i]);return Wt(A!=null,`<Router basename="${m}"> is not able to match the URL "${v}${h}${y}" because it does not start with the basename, so the <Router> won't render anything.`),A==null?null:g.createElement(en.Provider,{value:p},g.createElement(Nr.Provider,{children:u,value:A}))}function GS({children:l,location:u}){return MS(ps(l),u)}function ps(l,u=[]){let o=[];return g.Children.forEach(l,(i,s)=>{if(!g.isValidElement(i))return;let f=[...u,s];if(i.type===g.Fragment){o.push.apply(o,ps(i.props.children,f));return}He(i.type===hs,`[${typeof i.type=="string"?i.type:i.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),He(!i.props.index||!i.props.children,"An index route cannot have child routes.");let m={id:i.props.id||f.join("-"),caseSensitive:i.props.caseSensitive,element:i.props.element,Component:i.props.Component,index:i.props.index,path:i.props.path,loader:i.props.loader,action:i.props.action,hydrateFallbackElement:i.props.hydrateFallbackElement,HydrateFallback:i.props.HydrateFallback,errorElement:i.props.errorElement,ErrorBoundary:i.props.ErrorBoundary,hasErrorBoundary:i.props.hasErrorBoundary===!0||i.props.ErrorBoundary!=null||i.props.errorElement!=null,shouldRevalidate:i.props.shouldRevalidate,handle:i.props.handle,lazy:i.props.lazy};i.props.children&&(m.children=ps(i.props.children,f)),o.push(m)}),o}var si="get",fi="application/x-www-form-urlencoded";function Ri(l){return l!=null&&typeof l.tagName=="string"}function YS(l){return Ri(l)&&l.tagName.toLowerCase()==="button"}function qS(l){return Ri(l)&&l.tagName.toLowerCase()==="form"}function VS(l){return Ri(l)&&l.tagName.toLowerCase()==="input"}function XS(l){return!!(l.metaKey||l.altKey||l.ctrlKey||l.shiftKey)}function QS(l,u){return l.button===0&&(!u||u==="_self")&&!XS(l)}var ti=null;function ZS(){if(ti===null)try{new FormData(document.createElement("form"),0),ti=!1}catch{ti=!0}return ti}var KS=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function es(l){return l!=null&&!KS.has(l)?(Wt(!1,`"${l}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${fi}"`),null):l}function PS(l,u){let o,i,s,f,m;if(qS(l)){let p=l.getAttribute("action");i=p?Sn(p,u):null,o=l.getAttribute("method")||si,s=es(l.getAttribute("enctype"))||fi,f=new FormData(l)}else if(YS(l)||VS(l)&&(l.type==="submit"||l.type==="image")){let p=l.form;if(p==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=l.getAttribute("formaction")||p.getAttribute("action");if(i=v?Sn(v,u):null,o=l.getAttribute("formmethod")||p.getAttribute("method")||si,s=es(l.getAttribute("formenctype"))||es(p.getAttribute("enctype"))||fi,f=new FormData(p,l),!ZS()){let{name:h,type:y,value:S}=l;if(y==="image"){let E=h?`${h}.`:"";f.append(`${E}x`,"0"),f.append(`${E}y`,"0")}else h&&f.append(h,S)}}else{if(Ri(l))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');o=si,i=null,s=fi,m=l}return f&&s==="text/plain"&&(m=f,f=void 0),{action:i,method:o.toLowerCase(),encType:s,formData:f,body:m}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function zs(l,u){if(l===!1||l===null||typeof l>"u")throw new Error(u)}function $S(l,u,o){let i=typeof l=="string"?new URL(l,typeof window>"u"?"server://singlefetch/":window.location.origin):l;return i.pathname==="/"?i.pathname=`_root.${o}`:u&&Sn(i.pathname,u)==="/"?i.pathname=`${u.replace(/\/$/,"")}/_root.${o}`:i.pathname=`${i.pathname.replace(/\/$/,"")}.${o}`,i}async function JS(l,u){if(l.id in u)return u[l.id];try{let o=await import(l.module);return u[l.id]=o,o}catch(o){return console.error(`Error loading route module \`${l.module}\`, reloading page...`),console.error(o),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function FS(l){return l==null?!1:l.href==null?l.rel==="preload"&&typeof l.imageSrcSet=="string"&&typeof l.imageSizes=="string":typeof l.rel=="string"&&typeof l.href=="string"}async function WS(l,u,o){let i=await Promise.all(l.map(async s=>{let f=u.routes[s.route.id];if(f){let m=await JS(f,o);return m.links?m.links():[]}return[]}));return n1(i.flat(1).filter(FS).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function ip(l,u,o,i,s,f){let m=(v,h)=>o[h]?v.route.id!==o[h].route.id:!0,p=(v,h)=>o[h].pathname!==v.pathname||o[h].route.path?.endsWith("*")&&o[h].params["*"]!==v.params["*"];return f==="assets"?u.filter((v,h)=>m(v,h)||p(v,h)):f==="data"?u.filter((v,h)=>{let y=i.routes[v.route.id];if(!y||!y.hasLoader)return!1;if(m(v,h)||p(v,h))return!0;if(v.route.shouldRevalidate){let S=v.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:o[0]?.params||{},nextUrl:new URL(l,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof S=="boolean")return S}return!0}):[]}function IS(l,u,{includeHydrateFallback:o}={}){return e1(l.map(i=>{let s=u.routes[i.route.id];if(!s)return[];let f=[s.module];return s.clientActionModule&&(f=f.concat(s.clientActionModule)),s.clientLoaderModule&&(f=f.concat(s.clientLoaderModule)),o&&s.hydrateFallbackModule&&(f=f.concat(s.hydrateFallbackModule)),s.imports&&(f=f.concat(s.imports)),f}).flat(1))}function e1(l){return[...new Set(l)]}function t1(l){let u={},o=Object.keys(l).sort();for(let i of o)u[i]=l[i];return u}function n1(l,u){let o=new Set;return new Set(u),l.reduce((i,s)=>{let f=JSON.stringify(t1(s));return o.has(f)||(o.add(f),i.push({key:f,link:s})),i},[])}function nv(){let l=g.useContext(ba);return zs(l,"You must render this element inside a <DataRouterContext.Provider> element"),l}function l1(){let l=g.useContext(wi);return zs(l,"You must render this element inside a <DataRouterStateContext.Provider> element"),l}var Us=g.createContext(void 0);Us.displayName="FrameworkContext";function lv(){let l=g.useContext(Us);return zs(l,"You must render this element inside a <HydratedRouter> element"),l}function a1(l,u){let o=g.useContext(Us),[i,s]=g.useState(!1),[f,m]=g.useState(!1),{onFocus:p,onBlur:v,onMouseEnter:h,onMouseLeave:y,onTouchStart:S}=u,E=g.useRef(null);g.useEffect(()=>{if(l==="render"&&m(!0),l==="viewport"){let R=N=>{N.forEach(H=>{m(H.isIntersecting)})},T=new IntersectionObserver(R,{threshold:.5});return E.current&&T.observe(E.current),()=>{T.disconnect()}}},[l]),g.useEffect(()=>{if(i){let R=setTimeout(()=>{m(!0)},100);return()=>{clearTimeout(R)}}},[i]);let A=()=>{s(!0)},C=()=>{s(!1),m(!1)};return o?l!=="intent"?[f,E,{}]:[f,E,{onFocus:wr(p,A),onBlur:wr(v,C),onMouseEnter:wr(h,A),onMouseLeave:wr(y,C),onTouchStart:wr(S,A)}]:[!1,E,{}]}function wr(l,u){return o=>{l&&l(o),o.defaultPrevented||u(o)}}function r1({page:l,...u}){let{router:o}=nv(),i=g.useMemo(()=>Qp(o.routes,l,o.basename),[o.routes,l,o.basename]);return i?g.createElement(i1,{page:l,matches:i,...u}):null}function u1(l){let{manifest:u,routeModules:o}=lv(),[i,s]=g.useState([]);return g.useEffect(()=>{let f=!1;return WS(l,u,o).then(m=>{f||s(m)}),()=>{f=!0}},[l,u,o]),i}function i1({page:l,matches:u,...o}){let i=El(),{manifest:s,routeModules:f}=lv(),{basename:m}=nv(),{loaderData:p,matches:v}=l1(),h=g.useMemo(()=>ip(l,u,v,s,i,"data"),[l,u,v,s,i]),y=g.useMemo(()=>ip(l,u,v,s,i,"assets"),[l,u,v,s,i]),S=g.useMemo(()=>{if(l===i.pathname+i.search+i.hash)return[];let C=new Set,R=!1;if(u.forEach(N=>{let H=s.routes[N.route.id];!H||!H.hasLoader||(!h.some(k=>k.route.id===N.route.id)&&N.route.id in p&&f[N.route.id]?.shouldRevalidate||H.hasClientLoader?R=!0:C.add(N.route.id))}),C.size===0)return[];let T=$S(l,m,"data");return R&&C.size>0&&T.searchParams.set("_routes",u.filter(N=>C.has(N.route.id)).map(N=>N.route.id).join(",")),[T.pathname+T.search]},[m,p,i,s,h,u,l,f]),E=g.useMemo(()=>IS(y,s),[y,s]),A=u1(y);return g.createElement(g.Fragment,null,S.map(C=>g.createElement("link",{key:C,rel:"prefetch",as:"fetch",href:C,...o})),E.map(C=>g.createElement("link",{key:C,rel:"modulepreload",href:C,...o})),A.map(({key:C,link:R})=>g.createElement("link",{key:C,nonce:o.nonce,...R})))}function o1(...l){return u=>{l.forEach(o=>{typeof o=="function"?o(u):o!=null&&(o.current=u)})}}var av=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{av&&(window.__reactRouterVersion="7.9.1")}catch{}function c1({basename:l,children:u,window:o}){let i=g.useRef();i.current==null&&(i.current=Fb({window:o,v5Compat:!0}));let s=i.current,[f,m]=g.useState({action:s.action,location:s.location}),p=g.useCallback(v=>{g.startTransition(()=>m(v))},[m]);return g.useLayoutEffect(()=>s.listen(p),[s,p]),g.createElement(kS,{basename:l,children:u,location:f.location,navigationType:f.action,navigator:s})}var rv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,uv=g.forwardRef(function({onClick:u,discover:o="render",prefetch:i="none",relative:s,reloadDocument:f,replace:m,state:p,target:v,to:h,preventScrollReset:y,viewTransition:S,...E},A){let{basename:C}=g.useContext(en),R=typeof h=="string"&&rv.test(h),T,N=!1;if(typeof h=="string"&&R&&(T=h,av))try{let le=new URL(window.location.href),se=h.startsWith("//")?new URL(le.protocol+h):new URL(h),be=Sn(se.pathname,C);se.origin===le.origin&&be!=null?h=be+se.search+se.hash:N=!0}catch{Wt(!1,`<Link to="${h}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let H=RS(h,{relative:s}),[k,X,Y]=a1(i,E),W=m1(h,{replace:m,state:p,target:v,preventScrollReset:y,relative:s,viewTransition:S});function $(le){u&&u(le),le.defaultPrevented||W(le)}let Z=g.createElement("a",{...E,...Y,href:T||H,onClick:N||f?u:$,ref:o1(A,X),target:v,"data-discover":!R&&o==="render"?"true":void 0});return k&&!R?g.createElement(g.Fragment,null,Z,g.createElement(r1,{page:H})):Z});uv.displayName="Link";var s1=g.forwardRef(function({"aria-current":u="page",caseSensitive:o=!1,className:i="",end:s=!1,style:f,to:m,viewTransition:p,children:v,...h},y){let S=Ur(m,{relative:h.relative}),E=El(),A=g.useContext(wi),{navigator:C,basename:R}=g.useContext(en),T=A!=null&&y1(S)&&p===!0,N=C.encodeLocation?C.encodeLocation(S).pathname:S.pathname,H=E.pathname,k=A&&A.navigation&&A.navigation.location?A.navigation.location.pathname:null;o||(H=H.toLowerCase(),k=k?k.toLowerCase():null,N=N.toLowerCase()),k&&R&&(k=Sn(k,R)||k);const X=N!=="/"&&N.endsWith("/")?N.length-1:N.length;let Y=H===N||!s&&H.startsWith(N)&&H.charAt(X)==="/",W=k!=null&&(k===N||!s&&k.startsWith(N)&&k.charAt(N.length)==="/"),$={isActive:Y,isPending:W,isTransitioning:T},Z=Y?u:void 0,le;typeof i=="function"?le=i($):le=[i,Y?"active":null,W?"pending":null,T?"transitioning":null].filter(Boolean).join(" ");let se=typeof f=="function"?f($):f;return g.createElement(uv,{...h,"aria-current":Z,className:le,ref:y,style:se,to:m,viewTransition:p},typeof v=="function"?v($):v)});s1.displayName="NavLink";var f1=g.forwardRef(({discover:l="render",fetcherKey:u,navigate:o,reloadDocument:i,replace:s,state:f,method:m=si,action:p,onSubmit:v,relative:h,preventScrollReset:y,viewTransition:S,...E},A)=>{let C=v1(),R=g1(p,{relative:h}),T=m.toLowerCase()==="get"?"get":"post",N=typeof p=="string"&&rv.test(p),H=k=>{if(v&&v(k),k.defaultPrevented)return;k.preventDefault();let X=k.nativeEvent.submitter,Y=X?.getAttribute("formmethod")||m;C(X||k.currentTarget,{fetcherKey:u,method:Y,navigate:o,replace:s,state:f,relative:h,preventScrollReset:y,viewTransition:S})};return g.createElement("form",{ref:A,method:T,action:R,onSubmit:i?v:H,...E,"data-discover":!N&&l==="render"?"true":void 0})});f1.displayName="Form";function d1(l){return`${l} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function iv(l){let u=g.useContext(ba);return He(u,d1(l)),u}function m1(l,{target:u,replace:o,state:i,preventScrollReset:s,relative:f,viewTransition:m}={}){let p=_s(),v=El(),h=Ur(l,{relative:f});return g.useCallback(y=>{if(QS(y,u)){y.preventDefault();let S=o!==void 0?o:Mr(v)===Mr(h);p(l,{replace:S,state:i,preventScrollReset:s,relative:f,viewTransition:m})}},[v,p,h,o,i,u,l,s,f,m])}var h1=0,p1=()=>`__${String(++h1)}__`;function v1(){let{router:l}=iv("useSubmit"),{basename:u}=g.useContext(en),o=LS();return g.useCallback(async(i,s={})=>{let{action:f,method:m,encType:p,formData:v,body:h}=PS(i,u);if(s.navigate===!1){let y=s.fetcherKey||p1();await l.fetch(y,o,s.action||f,{preventScrollReset:s.preventScrollReset,formData:v,body:h,formMethod:s.method||m,formEncType:s.encType||p,flushSync:s.flushSync})}else await l.navigate(s.action||f,{preventScrollReset:s.preventScrollReset,formData:v,body:h,formMethod:s.method||m,formEncType:s.encType||p,replace:s.replace,state:s.state,fromRouteId:o,flushSync:s.flushSync,viewTransition:s.viewTransition})},[l,u,o])}function g1(l,{relative:u}={}){let{basename:o}=g.useContext(en),i=g.useContext(Rn);He(i,"useFormAction must be used inside a RouteContext");let[s]=i.matches.slice(-1),f={...Ur(l||".",{relative:u})},m=El();if(l==null){f.search=m.search;let p=new URLSearchParams(f.search),v=p.getAll("index");if(v.some(y=>y==="")){p.delete("index"),v.filter(S=>S).forEach(S=>p.append("index",S));let y=p.toString();f.search=y?`?${y}`:""}}return(!l||l===".")&&s.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),o!=="/"&&(f.pathname=f.pathname==="/"?o:bn([o,f.pathname])),Mr(f)}function y1(l,{relative:u}={}){let o=g.useContext(Fp);He(o!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:i}=iv("useViewTransitionState"),s=Ur(l,{relative:u});if(!o.isTransitioning)return!1;let f=Sn(o.currentLocation.pathname,i)||o.currentLocation.pathname,m=Sn(o.nextLocation.pathname,i)||o.nextLocation.pathname;return vi(s.pathname,m)!=null||vi(s.pathname,f)!=null}var Ls=Vp();const b1=Yp(Ls);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S1=l=>l.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),x1=l=>l.replace(/^([A-Z])|[\s-_]+(\w)/g,(u,o,i)=>i?i.toUpperCase():o.toLowerCase()),op=l=>{const u=x1(l);return u.charAt(0).toUpperCase()+u.slice(1)},ov=(...l)=>l.filter((u,o,i)=>!!u&&u.trim()!==""&&i.indexOf(u)===o).join(" ").trim(),E1=l=>{for(const u in l)if(u.startsWith("aria-")||u==="role"||u==="title")return!0};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var w1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R1=g.forwardRef(({color:l="currentColor",size:u=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:s="",children:f,iconNode:m,...p},v)=>g.createElement("svg",{ref:v,...w1,width:u,height:u,stroke:l,strokeWidth:i?Number(o)*24/Number(u):o,className:ov("lucide",s),...!f&&!E1(p)&&{"aria-hidden":"true"},...p},[...m.map(([h,y])=>g.createElement(h,y)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wl=(l,u)=>{const o=g.forwardRef(({className:i,...s},f)=>g.createElement(R1,{ref:f,iconNode:u,className:ov(`lucide-${S1(op(l))}`,`lucide-${l}`,i),...s}));return o.displayName=op(l),o};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A1=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],M1=wl("check",A1);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T1=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],C1=wl("chevron-right",T1);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O1=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],_1=wl("circle",O1);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D1=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],N1=wl("lock",D1);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z1=[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]],U1=wl("moon",z1);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L1=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],j1=wl("sun",L1);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H1=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],B1=wl("user",H1);function cp(l,u){if(typeof l=="function")return l(u);l!=null&&(l.current=u)}function Ai(...l){return u=>{let o=!1;const i=l.map(s=>{const f=cp(s,u);return!o&&typeof f=="function"&&(o=!0),f});if(o)return()=>{for(let s=0;s<i.length;s++){const f=i[s];typeof f=="function"?f():cp(l[s],null)}}}}function mt(...l){return g.useCallback(Ai(...l),l)}function Tr(l){const u=G1(l),o=g.forwardRef((i,s)=>{const{children:f,...m}=i,p=g.Children.toArray(f),v=p.find(q1);if(v){const h=v.props.children,y=p.map(S=>S===v?g.Children.count(h)>1?g.Children.only(null):g.isValidElement(h)?h.props.children:null:S);return _.jsx(u,{...m,ref:s,children:g.isValidElement(h)?g.cloneElement(h,void 0,y):null})}return _.jsx(u,{...m,ref:s,children:f})});return o.displayName=`${l}.Slot`,o}var k1=Tr("Slot");function G1(l){const u=g.forwardRef((o,i)=>{const{children:s,...f}=o;if(g.isValidElement(s)){const m=X1(s),p=V1(f,s.props);return s.type!==g.Fragment&&(p.ref=i?Ai(i,m):m),g.cloneElement(s,p)}return g.Children.count(s)>1?g.Children.only(null):null});return u.displayName=`${l}.SlotClone`,u}var Y1=Symbol("radix.slottable");function q1(l){return g.isValidElement(l)&&typeof l.type=="function"&&"__radixId"in l.type&&l.type.__radixId===Y1}function V1(l,u){const o={...u};for(const i in u){const s=l[i],f=u[i];/^on[A-Z]/.test(i)?s&&f?o[i]=(...p)=>{const v=f(...p);return s(...p),v}:s&&(o[i]=s):i==="style"?o[i]={...s,...f}:i==="className"&&(o[i]=[s,f].filter(Boolean).join(" "))}return{...l,...o}}function X1(l){let u=Object.getOwnPropertyDescriptor(l.props,"ref")?.get,o=u&&"isReactWarning"in u&&u.isReactWarning;return o?l.ref:(u=Object.getOwnPropertyDescriptor(l,"ref")?.get,o=u&&"isReactWarning"in u&&u.isReactWarning,o?l.props.ref:l.props.ref||l.ref)}function cv(l){var u,o,i="";if(typeof l=="string"||typeof l=="number")i+=l;else if(typeof l=="object")if(Array.isArray(l)){var s=l.length;for(u=0;u<s;u++)l[u]&&(o=cv(l[u]))&&(i&&(i+=" "),i+=o)}else for(o in l)l[o]&&(i&&(i+=" "),i+=o);return i}function sv(){for(var l,u,o=0,i="",s=arguments.length;o<s;o++)(l=arguments[o])&&(u=cv(l))&&(i&&(i+=" "),i+=u);return i}const sp=l=>typeof l=="boolean"?`${l}`:l===0?"0":l,fp=sv,js=(l,u)=>o=>{var i;if(u?.variants==null)return fp(l,o?.class,o?.className);const{variants:s,defaultVariants:f}=u,m=Object.keys(s).map(h=>{const y=o?.[h],S=f?.[h];if(y===null)return null;const E=sp(y)||sp(S);return s[h][E]}),p=o&&Object.entries(o).reduce((h,y)=>{let[S,E]=y;return E===void 0||(h[S]=E),h},{}),v=u==null||(i=u.compoundVariants)===null||i===void 0?void 0:i.reduce((h,y)=>{let{class:S,className:E,...A}=y;return Object.entries(A).every(C=>{let[R,T]=C;return Array.isArray(T)?T.includes({...f,...p}[R]):{...f,...p}[R]===T})?[...h,S,E]:h},[]);return fp(l,m,v,o?.class,o?.className)},Hs="-",Q1=l=>{const u=K1(l),{conflictingClassGroups:o,conflictingClassGroupModifiers:i}=l;return{getClassGroupId:m=>{const p=m.split(Hs);return p[0]===""&&p.length!==1&&p.shift(),fv(p,u)||Z1(m)},getConflictingClassGroupIds:(m,p)=>{const v=o[m]||[];return p&&i[m]?[...v,...i[m]]:v}}},fv=(l,u)=>{if(l.length===0)return u.classGroupId;const o=l[0],i=u.nextPart.get(o),s=i?fv(l.slice(1),i):void 0;if(s)return s;if(u.validators.length===0)return;const f=l.join(Hs);return u.validators.find(({validator:m})=>m(f))?.classGroupId},dp=/^\[(.+)\]$/,Z1=l=>{if(dp.test(l)){const u=dp.exec(l)[1],o=u?.substring(0,u.indexOf(":"));if(o)return"arbitrary.."+o}},K1=l=>{const{theme:u,classGroups:o}=l,i={nextPart:new Map,validators:[]};for(const s in o)vs(o[s],i,s,u);return i},vs=(l,u,o,i)=>{l.forEach(s=>{if(typeof s=="string"){const f=s===""?u:mp(u,s);f.classGroupId=o;return}if(typeof s=="function"){if(P1(s)){vs(s(i),u,o,i);return}u.validators.push({validator:s,classGroupId:o});return}Object.entries(s).forEach(([f,m])=>{vs(m,mp(u,f),o,i)})})},mp=(l,u)=>{let o=l;return u.split(Hs).forEach(i=>{o.nextPart.has(i)||o.nextPart.set(i,{nextPart:new Map,validators:[]}),o=o.nextPart.get(i)}),o},P1=l=>l.isThemeGetter,$1=l=>{if(l<1)return{get:()=>{},set:()=>{}};let u=0,o=new Map,i=new Map;const s=(f,m)=>{o.set(f,m),u++,u>l&&(u=0,i=o,o=new Map)};return{get(f){let m=o.get(f);if(m!==void 0)return m;if((m=i.get(f))!==void 0)return s(f,m),m},set(f,m){o.has(f)?o.set(f,m):s(f,m)}}},gs="!",ys=":",J1=ys.length,F1=l=>{const{prefix:u,experimentalParseClassName:o}=l;let i=s=>{const f=[];let m=0,p=0,v=0,h;for(let C=0;C<s.length;C++){let R=s[C];if(m===0&&p===0){if(R===ys){f.push(s.slice(v,C)),v=C+J1;continue}if(R==="/"){h=C;continue}}R==="["?m++:R==="]"?m--:R==="("?p++:R===")"&&p--}const y=f.length===0?s:s.substring(v),S=W1(y),E=S!==y,A=h&&h>v?h-v:void 0;return{modifiers:f,hasImportantModifier:E,baseClassName:S,maybePostfixModifierPosition:A}};if(u){const s=u+ys,f=i;i=m=>m.startsWith(s)?f(m.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:m,maybePostfixModifierPosition:void 0}}if(o){const s=i;i=f=>o({className:f,parseClassName:s})}return i},W1=l=>l.endsWith(gs)?l.substring(0,l.length-1):l.startsWith(gs)?l.substring(1):l,I1=l=>{const u=Object.fromEntries(l.orderSensitiveModifiers.map(i=>[i,!0]));return i=>{if(i.length<=1)return i;const s=[];let f=[];return i.forEach(m=>{m[0]==="["||u[m]?(s.push(...f.sort(),m),f=[]):f.push(m)}),s.push(...f.sort()),s}},ex=l=>({cache:$1(l.cacheSize),parseClassName:F1(l),sortModifiers:I1(l),...Q1(l)}),tx=/\s+/,nx=(l,u)=>{const{parseClassName:o,getClassGroupId:i,getConflictingClassGroupIds:s,sortModifiers:f}=u,m=[],p=l.trim().split(tx);let v="";for(let h=p.length-1;h>=0;h-=1){const y=p[h],{isExternal:S,modifiers:E,hasImportantModifier:A,baseClassName:C,maybePostfixModifierPosition:R}=o(y);if(S){v=y+(v.length>0?" "+v:v);continue}let T=!!R,N=i(T?C.substring(0,R):C);if(!N){if(!T){v=y+(v.length>0?" "+v:v);continue}if(N=i(C),!N){v=y+(v.length>0?" "+v:v);continue}T=!1}const H=f(E).join(":"),k=A?H+gs:H,X=k+N;if(m.includes(X))continue;m.push(X);const Y=s(N,T);for(let W=0;W<Y.length;++W){const $=Y[W];m.push(k+$)}v=y+(v.length>0?" "+v:v)}return v};function lx(){let l=0,u,o,i="";for(;l<arguments.length;)(u=arguments[l++])&&(o=dv(u))&&(i&&(i+=" "),i+=o);return i}const dv=l=>{if(typeof l=="string")return l;let u,o="";for(let i=0;i<l.length;i++)l[i]&&(u=dv(l[i]))&&(o&&(o+=" "),o+=u);return o};function ax(l,...u){let o,i,s,f=m;function m(v){const h=u.reduce((y,S)=>S(y),l());return o=ex(h),i=o.cache.get,s=o.cache.set,f=p,p(v)}function p(v){const h=i(v);if(h)return h;const y=nx(v,o);return s(v,y),y}return function(){return f(lx.apply(null,arguments))}}const Ze=l=>{const u=o=>o[l]||[];return u.isThemeGetter=!0,u},mv=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,hv=/^\((?:(\w[\w-]*):)?(.+)\)$/i,rx=/^\d+\/\d+$/,ux=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ix=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ox=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,cx=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,sx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,fa=l=>rx.test(l),ve=l=>!!l&&!Number.isNaN(Number(l)),Jn=l=>!!l&&Number.isInteger(Number(l)),ts=l=>l.endsWith("%")&&ve(l.slice(0,-1)),yn=l=>ux.test(l),fx=()=>!0,dx=l=>ix.test(l)&&!ox.test(l),pv=()=>!1,mx=l=>cx.test(l),hx=l=>sx.test(l),px=l=>!te(l)&&!ne(l),vx=l=>Sa(l,yv,pv),te=l=>mv.test(l),Sl=l=>Sa(l,bv,dx),ns=l=>Sa(l,xx,ve),hp=l=>Sa(l,vv,pv),gx=l=>Sa(l,gv,hx),ni=l=>Sa(l,Sv,mx),ne=l=>hv.test(l),Rr=l=>xa(l,bv),yx=l=>xa(l,Ex),pp=l=>xa(l,vv),bx=l=>xa(l,yv),Sx=l=>xa(l,gv),li=l=>xa(l,Sv,!0),Sa=(l,u,o)=>{const i=mv.exec(l);return i?i[1]?u(i[1]):o(i[2]):!1},xa=(l,u,o=!1)=>{const i=hv.exec(l);return i?i[1]?u(i[1]):o:!1},vv=l=>l==="position"||l==="percentage",gv=l=>l==="image"||l==="url",yv=l=>l==="length"||l==="size"||l==="bg-size",bv=l=>l==="length",xx=l=>l==="number",Ex=l=>l==="family-name",Sv=l=>l==="shadow",wx=()=>{const l=Ze("color"),u=Ze("font"),o=Ze("text"),i=Ze("font-weight"),s=Ze("tracking"),f=Ze("leading"),m=Ze("breakpoint"),p=Ze("container"),v=Ze("spacing"),h=Ze("radius"),y=Ze("shadow"),S=Ze("inset-shadow"),E=Ze("text-shadow"),A=Ze("drop-shadow"),C=Ze("blur"),R=Ze("perspective"),T=Ze("aspect"),N=Ze("ease"),H=Ze("animate"),k=()=>["auto","avoid","all","avoid-page","page","left","right","column"],X=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],Y=()=>[...X(),ne,te],W=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],Z=()=>[ne,te,v],le=()=>[fa,"full","auto",...Z()],se=()=>[Jn,"none","subgrid",ne,te],be=()=>["auto",{span:["full",Jn,ne,te]},Jn,ne,te],fe=()=>[Jn,"auto",ne,te],Ee=()=>["auto","min","max","fr",ne,te],ge=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],me=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...Z()],K=()=>[fa,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Z()],B=()=>[l,ne,te],J=()=>[...X(),pp,hp,{position:[ne,te]}],w=()=>["no-repeat",{repeat:["","x","y","space","round"]}],V=()=>["auto","cover","contain",bx,vx,{size:[ne,te]}],F=()=>[ts,Rr,Sl],P=()=>["","none","full",h,ne,te],I=()=>["",ve,Rr,Sl],de=()=>["solid","dashed","dotted","double"],re=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ue=()=>[ve,ts,pp,hp],Ae=()=>["","none",C,ne,te],at=()=>["none",ve,ne,te],Gt=()=>["none",ve,ne,te],Yt=()=>[ve,ne,te],qt=()=>[fa,"full",...Z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[yn],breakpoint:[yn],color:[fx],container:[yn],"drop-shadow":[yn],ease:["in","out","in-out"],font:[px],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[yn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[yn],shadow:[yn],spacing:["px",ve],text:[yn],"text-shadow":[yn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",fa,te,ne,T]}],container:["container"],columns:[{columns:[ve,te,ne,p]}],"break-after":[{"break-after":k()}],"break-before":[{"break-before":k()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:Y()}],overflow:[{overflow:W()}],"overflow-x":[{"overflow-x":W()}],"overflow-y":[{"overflow-y":W()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:le()}],"inset-x":[{"inset-x":le()}],"inset-y":[{"inset-y":le()}],start:[{start:le()}],end:[{end:le()}],top:[{top:le()}],right:[{right:le()}],bottom:[{bottom:le()}],left:[{left:le()}],visibility:["visible","invisible","collapse"],z:[{z:[Jn,"auto",ne,te]}],basis:[{basis:[fa,"full","auto",p,...Z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ve,fa,"auto","initial","none",te]}],grow:[{grow:["",ve,ne,te]}],shrink:[{shrink:["",ve,ne,te]}],order:[{order:[Jn,"first","last","none",ne,te]}],"grid-cols":[{"grid-cols":se()}],"col-start-end":[{col:be()}],"col-start":[{"col-start":fe()}],"col-end":[{"col-end":fe()}],"grid-rows":[{"grid-rows":se()}],"row-start-end":[{row:be()}],"row-start":[{"row-start":fe()}],"row-end":[{"row-end":fe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Ee()}],"auto-rows":[{"auto-rows":Ee()}],gap:[{gap:Z()}],"gap-x":[{"gap-x":Z()}],"gap-y":[{"gap-y":Z()}],"justify-content":[{justify:[...ge(),"normal"]}],"justify-items":[{"justify-items":[...me(),"normal"]}],"justify-self":[{"justify-self":["auto",...me()]}],"align-content":[{content:["normal",...ge()]}],"align-items":[{items:[...me(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...me(),{baseline:["","last"]}]}],"place-content":[{"place-content":ge()}],"place-items":[{"place-items":[...me(),"baseline"]}],"place-self":[{"place-self":["auto",...me()]}],p:[{p:Z()}],px:[{px:Z()}],py:[{py:Z()}],ps:[{ps:Z()}],pe:[{pe:Z()}],pt:[{pt:Z()}],pr:[{pr:Z()}],pb:[{pb:Z()}],pl:[{pl:Z()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":Z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Z()}],"space-y-reverse":["space-y-reverse"],size:[{size:K()}],w:[{w:[p,"screen",...K()]}],"min-w":[{"min-w":[p,"screen","none",...K()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[m]},...K()]}],h:[{h:["screen","lh",...K()]}],"min-h":[{"min-h":["screen","lh","none",...K()]}],"max-h":[{"max-h":["screen","lh",...K()]}],"font-size":[{text:["base",o,Rr,Sl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[i,ne,ns]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ts,te]}],"font-family":[{font:[yx,te,u]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,ne,te]}],"line-clamp":[{"line-clamp":[ve,"none",ne,ns]}],leading:[{leading:[f,...Z()]}],"list-image":[{"list-image":["none",ne,te]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ne,te]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:B()}],"text-color":[{text:B()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...de(),"wavy"]}],"text-decoration-thickness":[{decoration:[ve,"from-font","auto",ne,Sl]}],"text-decoration-color":[{decoration:B()}],"underline-offset":[{"underline-offset":[ve,"auto",ne,te]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ne,te]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ne,te]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:J()}],"bg-repeat":[{bg:w()}],"bg-size":[{bg:V()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Jn,ne,te],radial:["",ne,te],conic:[Jn,ne,te]},Sx,gx]}],"bg-color":[{bg:B()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:B()}],"gradient-via":[{via:B()}],"gradient-to":[{to:B()}],rounded:[{rounded:P()}],"rounded-s":[{"rounded-s":P()}],"rounded-e":[{"rounded-e":P()}],"rounded-t":[{"rounded-t":P()}],"rounded-r":[{"rounded-r":P()}],"rounded-b":[{"rounded-b":P()}],"rounded-l":[{"rounded-l":P()}],"rounded-ss":[{"rounded-ss":P()}],"rounded-se":[{"rounded-se":P()}],"rounded-ee":[{"rounded-ee":P()}],"rounded-es":[{"rounded-es":P()}],"rounded-tl":[{"rounded-tl":P()}],"rounded-tr":[{"rounded-tr":P()}],"rounded-br":[{"rounded-br":P()}],"rounded-bl":[{"rounded-bl":P()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...de(),"hidden","none"]}],"divide-style":[{divide:[...de(),"hidden","none"]}],"border-color":[{border:B()}],"border-color-x":[{"border-x":B()}],"border-color-y":[{"border-y":B()}],"border-color-s":[{"border-s":B()}],"border-color-e":[{"border-e":B()}],"border-color-t":[{"border-t":B()}],"border-color-r":[{"border-r":B()}],"border-color-b":[{"border-b":B()}],"border-color-l":[{"border-l":B()}],"divide-color":[{divide:B()}],"outline-style":[{outline:[...de(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ve,ne,te]}],"outline-w":[{outline:["",ve,Rr,Sl]}],"outline-color":[{outline:B()}],shadow:[{shadow:["","none",y,li,ni]}],"shadow-color":[{shadow:B()}],"inset-shadow":[{"inset-shadow":["none",S,li,ni]}],"inset-shadow-color":[{"inset-shadow":B()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:B()}],"ring-offset-w":[{"ring-offset":[ve,Sl]}],"ring-offset-color":[{"ring-offset":B()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":B()}],"text-shadow":[{"text-shadow":["none",E,li,ni]}],"text-shadow-color":[{"text-shadow":B()}],opacity:[{opacity:[ve,ne,te]}],"mix-blend":[{"mix-blend":[...re(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":re()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ve]}],"mask-image-linear-from-pos":[{"mask-linear-from":ue()}],"mask-image-linear-to-pos":[{"mask-linear-to":ue()}],"mask-image-linear-from-color":[{"mask-linear-from":B()}],"mask-image-linear-to-color":[{"mask-linear-to":B()}],"mask-image-t-from-pos":[{"mask-t-from":ue()}],"mask-image-t-to-pos":[{"mask-t-to":ue()}],"mask-image-t-from-color":[{"mask-t-from":B()}],"mask-image-t-to-color":[{"mask-t-to":B()}],"mask-image-r-from-pos":[{"mask-r-from":ue()}],"mask-image-r-to-pos":[{"mask-r-to":ue()}],"mask-image-r-from-color":[{"mask-r-from":B()}],"mask-image-r-to-color":[{"mask-r-to":B()}],"mask-image-b-from-pos":[{"mask-b-from":ue()}],"mask-image-b-to-pos":[{"mask-b-to":ue()}],"mask-image-b-from-color":[{"mask-b-from":B()}],"mask-image-b-to-color":[{"mask-b-to":B()}],"mask-image-l-from-pos":[{"mask-l-from":ue()}],"mask-image-l-to-pos":[{"mask-l-to":ue()}],"mask-image-l-from-color":[{"mask-l-from":B()}],"mask-image-l-to-color":[{"mask-l-to":B()}],"mask-image-x-from-pos":[{"mask-x-from":ue()}],"mask-image-x-to-pos":[{"mask-x-to":ue()}],"mask-image-x-from-color":[{"mask-x-from":B()}],"mask-image-x-to-color":[{"mask-x-to":B()}],"mask-image-y-from-pos":[{"mask-y-from":ue()}],"mask-image-y-to-pos":[{"mask-y-to":ue()}],"mask-image-y-from-color":[{"mask-y-from":B()}],"mask-image-y-to-color":[{"mask-y-to":B()}],"mask-image-radial":[{"mask-radial":[ne,te]}],"mask-image-radial-from-pos":[{"mask-radial-from":ue()}],"mask-image-radial-to-pos":[{"mask-radial-to":ue()}],"mask-image-radial-from-color":[{"mask-radial-from":B()}],"mask-image-radial-to-color":[{"mask-radial-to":B()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":X()}],"mask-image-conic-pos":[{"mask-conic":[ve]}],"mask-image-conic-from-pos":[{"mask-conic-from":ue()}],"mask-image-conic-to-pos":[{"mask-conic-to":ue()}],"mask-image-conic-from-color":[{"mask-conic-from":B()}],"mask-image-conic-to-color":[{"mask-conic-to":B()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:J()}],"mask-repeat":[{mask:w()}],"mask-size":[{mask:V()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ne,te]}],filter:[{filter:["","none",ne,te]}],blur:[{blur:Ae()}],brightness:[{brightness:[ve,ne,te]}],contrast:[{contrast:[ve,ne,te]}],"drop-shadow":[{"drop-shadow":["","none",A,li,ni]}],"drop-shadow-color":[{"drop-shadow":B()}],grayscale:[{grayscale:["",ve,ne,te]}],"hue-rotate":[{"hue-rotate":[ve,ne,te]}],invert:[{invert:["",ve,ne,te]}],saturate:[{saturate:[ve,ne,te]}],sepia:[{sepia:["",ve,ne,te]}],"backdrop-filter":[{"backdrop-filter":["","none",ne,te]}],"backdrop-blur":[{"backdrop-blur":Ae()}],"backdrop-brightness":[{"backdrop-brightness":[ve,ne,te]}],"backdrop-contrast":[{"backdrop-contrast":[ve,ne,te]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ve,ne,te]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ve,ne,te]}],"backdrop-invert":[{"backdrop-invert":["",ve,ne,te]}],"backdrop-opacity":[{"backdrop-opacity":[ve,ne,te]}],"backdrop-saturate":[{"backdrop-saturate":[ve,ne,te]}],"backdrop-sepia":[{"backdrop-sepia":["",ve,ne,te]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Z()}],"border-spacing-x":[{"border-spacing-x":Z()}],"border-spacing-y":[{"border-spacing-y":Z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ne,te]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ve,"initial",ne,te]}],ease:[{ease:["linear","initial",N,ne,te]}],delay:[{delay:[ve,ne,te]}],animate:[{animate:["none",H,ne,te]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[R,ne,te]}],"perspective-origin":[{"perspective-origin":Y()}],rotate:[{rotate:at()}],"rotate-x":[{"rotate-x":at()}],"rotate-y":[{"rotate-y":at()}],"rotate-z":[{"rotate-z":at()}],scale:[{scale:Gt()}],"scale-x":[{"scale-x":Gt()}],"scale-y":[{"scale-y":Gt()}],"scale-z":[{"scale-z":Gt()}],"scale-3d":["scale-3d"],skew:[{skew:Yt()}],"skew-x":[{"skew-x":Yt()}],"skew-y":[{"skew-y":Yt()}],transform:[{transform:[ne,te,"","none","gpu","cpu"]}],"transform-origin":[{origin:Y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:qt()}],"translate-x":[{"translate-x":qt()}],"translate-y":[{"translate-y":qt()}],"translate-z":[{"translate-z":qt()}],"translate-none":["translate-none"],accent:[{accent:B()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:B()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ne,te]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Z()}],"scroll-mx":[{"scroll-mx":Z()}],"scroll-my":[{"scroll-my":Z()}],"scroll-ms":[{"scroll-ms":Z()}],"scroll-me":[{"scroll-me":Z()}],"scroll-mt":[{"scroll-mt":Z()}],"scroll-mr":[{"scroll-mr":Z()}],"scroll-mb":[{"scroll-mb":Z()}],"scroll-ml":[{"scroll-ml":Z()}],"scroll-p":[{"scroll-p":Z()}],"scroll-px":[{"scroll-px":Z()}],"scroll-py":[{"scroll-py":Z()}],"scroll-ps":[{"scroll-ps":Z()}],"scroll-pe":[{"scroll-pe":Z()}],"scroll-pt":[{"scroll-pt":Z()}],"scroll-pr":[{"scroll-pr":Z()}],"scroll-pb":[{"scroll-pb":Z()}],"scroll-pl":[{"scroll-pl":Z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ne,te]}],fill:[{fill:["none",...B()]}],"stroke-w":[{stroke:[ve,Rr,Sl,ns]}],stroke:[{stroke:["none",...B()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Rx=ax(wx);function et(...l){return Rx(sv(l))}const Ax=js("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",callToAction:"bg-callToAction text-callToAction-foreground hover:bg-callToAction/90",destructive:"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Bs({className:l,variant:u,size:o,asChild:i=!1,...s}){const f=i?k1:"button";return _.jsx(f,{"data-slot":"button",className:et(Ax({variant:u,size:o,className:l})),...s})}function Re(l,u,{checkForDefaultPrevented:o=!0}={}){return function(s){if(l?.(s),o===!1||!s.defaultPrevented)return u?.(s)}}function Lr(l,u=[]){let o=[];function i(f,m){const p=g.createContext(m),v=o.length;o=[...o,m];const h=S=>{const{scope:E,children:A,...C}=S,R=E?.[l]?.[v]||p,T=g.useMemo(()=>C,Object.values(C));return _.jsx(R.Provider,{value:T,children:A})};h.displayName=f+"Provider";function y(S,E){const A=E?.[l]?.[v]||p,C=g.useContext(A);if(C)return C;if(m!==void 0)return m;throw new Error(`\`${S}\` must be used within \`${f}\``)}return[h,y]}const s=()=>{const f=o.map(m=>g.createContext(m));return function(p){const v=p?.[l]||f;return g.useMemo(()=>({[`__scope${l}`]:{...p,[l]:v}}),[p,v])}};return s.scopeName=l,[i,Mx(s,...u)]}function Mx(...l){const u=l[0];if(l.length===1)return u;const o=()=>{const i=l.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(f){const m=i.reduce((p,{useScope:v,scopeName:h})=>{const S=v(f)[`__scope${h}`];return{...p,...S}},{});return g.useMemo(()=>({[`__scope${u.scopeName}`]:m}),[m])}};return o.scopeName=u.scopeName,o}var In=globalThis?.document?g.useLayoutEffect:()=>{},Tx=qp[" useInsertionEffect ".trim().toString()]||In;function xv({prop:l,defaultProp:u,onChange:o=()=>{},caller:i}){const[s,f,m]=Cx({defaultProp:u,onChange:o}),p=l!==void 0,v=p?l:s;{const y=g.useRef(l!==void 0);g.useEffect(()=>{const S=y.current;S!==p&&console.warn(`${i} is changing from ${S?"controlled":"uncontrolled"} to ${p?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),y.current=p},[p,i])}const h=g.useCallback(y=>{if(p){const S=Ox(y)?y(l):y;S!==l&&m.current?.(S)}else f(y)},[p,l,f,m]);return[v,h]}function Cx({defaultProp:l,onChange:u}){const[o,i]=g.useState(l),s=g.useRef(o),f=g.useRef(u);return Tx(()=>{f.current=u},[u]),g.useEffect(()=>{s.current!==o&&(f.current?.(o),s.current=o)},[o,s]),[o,i,f]}function Ox(l){return typeof l=="function"}var _x=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],ut=_x.reduce((l,u)=>{const o=Tr(`Primitive.${u}`),i=g.forwardRef((s,f)=>{const{asChild:m,...p}=s,v=m?o:u;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),_.jsx(v,{...p,ref:f})});return i.displayName=`Primitive.${u}`,{...l,[u]:i}},{});function Ev(l,u){l&&Ls.flushSync(()=>l.dispatchEvent(u))}function wv(l){const u=l+"CollectionProvider",[o,i]=Lr(u),[s,f]=o(u,{collectionRef:{current:null},itemMap:new Map}),m=R=>{const{scope:T,children:N}=R,H=Fn.useRef(null),k=Fn.useRef(new Map).current;return _.jsx(s,{scope:T,itemMap:k,collectionRef:H,children:N})};m.displayName=u;const p=l+"CollectionSlot",v=Tr(p),h=Fn.forwardRef((R,T)=>{const{scope:N,children:H}=R,k=f(p,N),X=mt(T,k.collectionRef);return _.jsx(v,{ref:X,children:H})});h.displayName=p;const y=l+"CollectionItemSlot",S="data-radix-collection-item",E=Tr(y),A=Fn.forwardRef((R,T)=>{const{scope:N,children:H,...k}=R,X=Fn.useRef(null),Y=mt(T,X),W=f(y,N);return Fn.useEffect(()=>(W.itemMap.set(X,{ref:X,...k}),()=>void W.itemMap.delete(X))),_.jsx(E,{[S]:"",ref:Y,children:H})});A.displayName=y;function C(R){const T=f(l+"CollectionConsumer",R);return Fn.useCallback(()=>{const H=T.collectionRef.current;if(!H)return[];const k=Array.from(H.querySelectorAll(`[${S}]`));return Array.from(T.itemMap.values()).sort((W,$)=>k.indexOf(W.ref.current)-k.indexOf($.ref.current))},[T.collectionRef,T.itemMap])}return[{Provider:m,Slot:h,ItemSlot:A},C,i]}var Dx=g.createContext(void 0);function Rv(l){const u=g.useContext(Dx);return l||u||"ltr"}function xn(l){const u=g.useRef(l);return g.useEffect(()=>{u.current=l}),g.useMemo(()=>(...o)=>u.current?.(...o),[])}function Nx(l,u=globalThis?.document){const o=xn(l);g.useEffect(()=>{const i=s=>{s.key==="Escape"&&o(s)};return u.addEventListener("keydown",i,{capture:!0}),()=>u.removeEventListener("keydown",i,{capture:!0})},[o,u])}var zx="DismissableLayer",bs="dismissableLayer.update",Ux="dismissableLayer.pointerDownOutside",Lx="dismissableLayer.focusOutside",vp,Av=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Mv=g.forwardRef((l,u)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:f,onInteractOutside:m,onDismiss:p,...v}=l,h=g.useContext(Av),[y,S]=g.useState(null),E=y?.ownerDocument??globalThis?.document,[,A]=g.useState({}),C=mt(u,$=>S($)),R=Array.from(h.layers),[T]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),N=R.indexOf(T),H=y?R.indexOf(y):-1,k=h.layersWithOutsidePointerEventsDisabled.size>0,X=H>=N,Y=Bx($=>{const Z=$.target,le=[...h.branches].some(se=>se.contains(Z));!X||le||(s?.($),m?.($),$.defaultPrevented||p?.())},E),W=kx($=>{const Z=$.target;[...h.branches].some(se=>se.contains(Z))||(f?.($),m?.($),$.defaultPrevented||p?.())},E);return Nx($=>{H===h.layers.size-1&&(i?.($),!$.defaultPrevented&&p&&($.preventDefault(),p()))},E),g.useEffect(()=>{if(y)return o&&(h.layersWithOutsidePointerEventsDisabled.size===0&&(vp=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(y)),h.layers.add(y),gp(),()=>{o&&h.layersWithOutsidePointerEventsDisabled.size===1&&(E.body.style.pointerEvents=vp)}},[y,E,o,h]),g.useEffect(()=>()=>{y&&(h.layers.delete(y),h.layersWithOutsidePointerEventsDisabled.delete(y),gp())},[y,h]),g.useEffect(()=>{const $=()=>A({});return document.addEventListener(bs,$),()=>document.removeEventListener(bs,$)},[]),_.jsx(ut.div,{...v,ref:C,style:{pointerEvents:k?X?"auto":"none":void 0,...l.style},onFocusCapture:Re(l.onFocusCapture,W.onFocusCapture),onBlurCapture:Re(l.onBlurCapture,W.onBlurCapture),onPointerDownCapture:Re(l.onPointerDownCapture,Y.onPointerDownCapture)})});Mv.displayName=zx;var jx="DismissableLayerBranch",Hx=g.forwardRef((l,u)=>{const o=g.useContext(Av),i=g.useRef(null),s=mt(u,i);return g.useEffect(()=>{const f=i.current;if(f)return o.branches.add(f),()=>{o.branches.delete(f)}},[o.branches]),_.jsx(ut.div,{...l,ref:s})});Hx.displayName=jx;function Bx(l,u=globalThis?.document){const o=xn(l),i=g.useRef(!1),s=g.useRef(()=>{});return g.useEffect(()=>{const f=p=>{if(p.target&&!i.current){let v=function(){Tv(Ux,o,h,{discrete:!0})};const h={originalEvent:p};p.pointerType==="touch"?(u.removeEventListener("click",s.current),s.current=v,u.addEventListener("click",s.current,{once:!0})):v()}else u.removeEventListener("click",s.current);i.current=!1},m=window.setTimeout(()=>{u.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(m),u.removeEventListener("pointerdown",f),u.removeEventListener("click",s.current)}},[u,o]),{onPointerDownCapture:()=>i.current=!0}}function kx(l,u=globalThis?.document){const o=xn(l),i=g.useRef(!1);return g.useEffect(()=>{const s=f=>{f.target&&!i.current&&Tv(Lx,o,{originalEvent:f},{discrete:!1})};return u.addEventListener("focusin",s),()=>u.removeEventListener("focusin",s)},[u,o]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}function gp(){const l=new CustomEvent(bs);document.dispatchEvent(l)}function Tv(l,u,o,{discrete:i}){const s=o.originalEvent.target,f=new CustomEvent(l,{bubbles:!1,cancelable:!0,detail:o});u&&s.addEventListener(l,u,{once:!0}),i?Ev(s,f):s.dispatchEvent(f)}var ls=0;function Gx(){g.useEffect(()=>{const l=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",l[0]??yp()),document.body.insertAdjacentElement("beforeend",l[1]??yp()),ls++,()=>{ls===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(u=>u.remove()),ls--}},[])}function yp(){const l=document.createElement("span");return l.setAttribute("data-radix-focus-guard",""),l.tabIndex=0,l.style.outline="none",l.style.opacity="0",l.style.position="fixed",l.style.pointerEvents="none",l}var as="focusScope.autoFocusOnMount",rs="focusScope.autoFocusOnUnmount",bp={bubbles:!1,cancelable:!0},Yx="FocusScope",Cv=g.forwardRef((l,u)=>{const{loop:o=!1,trapped:i=!1,onMountAutoFocus:s,onUnmountAutoFocus:f,...m}=l,[p,v]=g.useState(null),h=xn(s),y=xn(f),S=g.useRef(null),E=mt(u,R=>v(R)),A=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(i){let R=function(k){if(A.paused||!p)return;const X=k.target;p.contains(X)?S.current=X:Wn(S.current,{select:!0})},T=function(k){if(A.paused||!p)return;const X=k.relatedTarget;X!==null&&(p.contains(X)||Wn(S.current,{select:!0}))},N=function(k){if(document.activeElement===document.body)for(const Y of k)Y.removedNodes.length>0&&Wn(p)};document.addEventListener("focusin",R),document.addEventListener("focusout",T);const H=new MutationObserver(N);return p&&H.observe(p,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",R),document.removeEventListener("focusout",T),H.disconnect()}}},[i,p,A.paused]),g.useEffect(()=>{if(p){xp.add(A);const R=document.activeElement;if(!p.contains(R)){const N=new CustomEvent(as,bp);p.addEventListener(as,h),p.dispatchEvent(N),N.defaultPrevented||(qx(Kx(Ov(p)),{select:!0}),document.activeElement===R&&Wn(p))}return()=>{p.removeEventListener(as,h),setTimeout(()=>{const N=new CustomEvent(rs,bp);p.addEventListener(rs,y),p.dispatchEvent(N),N.defaultPrevented||Wn(R??document.body,{select:!0}),p.removeEventListener(rs,y),xp.remove(A)},0)}}},[p,h,y,A]);const C=g.useCallback(R=>{if(!o&&!i||A.paused)return;const T=R.key==="Tab"&&!R.altKey&&!R.ctrlKey&&!R.metaKey,N=document.activeElement;if(T&&N){const H=R.currentTarget,[k,X]=Vx(H);k&&X?!R.shiftKey&&N===X?(R.preventDefault(),o&&Wn(k,{select:!0})):R.shiftKey&&N===k&&(R.preventDefault(),o&&Wn(X,{select:!0})):N===H&&R.preventDefault()}},[o,i,A.paused]);return _.jsx(ut.div,{tabIndex:-1,...m,ref:E,onKeyDown:C})});Cv.displayName=Yx;function qx(l,{select:u=!1}={}){const o=document.activeElement;for(const i of l)if(Wn(i,{select:u}),document.activeElement!==o)return}function Vx(l){const u=Ov(l),o=Sp(u,l),i=Sp(u.reverse(),l);return[o,i]}function Ov(l){const u=[],o=document.createTreeWalker(l,NodeFilter.SHOW_ELEMENT,{acceptNode:i=>{const s=i.tagName==="INPUT"&&i.type==="hidden";return i.disabled||i.hidden||s?NodeFilter.FILTER_SKIP:i.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)u.push(o.currentNode);return u}function Sp(l,u){for(const o of l)if(!Xx(o,{upTo:u}))return o}function Xx(l,{upTo:u}){if(getComputedStyle(l).visibility==="hidden")return!0;for(;l;){if(u!==void 0&&l===u)return!1;if(getComputedStyle(l).display==="none")return!0;l=l.parentElement}return!1}function Qx(l){return l instanceof HTMLInputElement&&"select"in l}function Wn(l,{select:u=!1}={}){if(l&&l.focus){const o=document.activeElement;l.focus({preventScroll:!0}),l!==o&&Qx(l)&&u&&l.select()}}var xp=Zx();function Zx(){let l=[];return{add(u){const o=l[0];u!==o&&o?.pause(),l=Ep(l,u),l.unshift(u)},remove(u){l=Ep(l,u),l[0]?.resume()}}}function Ep(l,u){const o=[...l],i=o.indexOf(u);return i!==-1&&o.splice(i,1),o}function Kx(l){return l.filter(u=>u.tagName!=="A")}var Px=qp[" useId ".trim().toString()]||(()=>{}),$x=0;function Ss(l){const[u,o]=g.useState(Px());return In(()=>{o(i=>i??String($x++))},[l]),u?`radix-${u}`:""}const Jx=["top","right","bottom","left"],el=Math.min,wt=Math.max,gi=Math.round,ai=Math.floor,Ft=l=>({x:l,y:l}),Fx={left:"right",right:"left",bottom:"top",top:"bottom"},Wx={start:"end",end:"start"};function xs(l,u,o){return wt(l,el(u,o))}function En(l,u){return typeof l=="function"?l(u):l}function wn(l){return l.split("-")[0]}function Ea(l){return l.split("-")[1]}function ks(l){return l==="x"?"y":"x"}function Gs(l){return l==="y"?"height":"width"}const Ix=new Set(["top","bottom"]);function Jt(l){return Ix.has(wn(l))?"y":"x"}function Ys(l){return ks(Jt(l))}function eE(l,u,o){o===void 0&&(o=!1);const i=Ea(l),s=Ys(l),f=Gs(s);let m=s==="x"?i===(o?"end":"start")?"right":"left":i==="start"?"bottom":"top";return u.reference[f]>u.floating[f]&&(m=yi(m)),[m,yi(m)]}function tE(l){const u=yi(l);return[Es(l),u,Es(u)]}function Es(l){return l.replace(/start|end/g,u=>Wx[u])}const wp=["left","right"],Rp=["right","left"],nE=["top","bottom"],lE=["bottom","top"];function aE(l,u,o){switch(l){case"top":case"bottom":return o?u?Rp:wp:u?wp:Rp;case"left":case"right":return u?nE:lE;default:return[]}}function rE(l,u,o,i){const s=Ea(l);let f=aE(wn(l),o==="start",i);return s&&(f=f.map(m=>m+"-"+s),u&&(f=f.concat(f.map(Es)))),f}function yi(l){return l.replace(/left|right|bottom|top/g,u=>Fx[u])}function uE(l){return{top:0,right:0,bottom:0,left:0,...l}}function _v(l){return typeof l!="number"?uE(l):{top:l,right:l,bottom:l,left:l}}function bi(l){const{x:u,y:o,width:i,height:s}=l;return{width:i,height:s,top:o,left:u,right:u+i,bottom:o+s,x:u,y:o}}function Ap(l,u,o){let{reference:i,floating:s}=l;const f=Jt(u),m=Ys(u),p=Gs(m),v=wn(u),h=f==="y",y=i.x+i.width/2-s.width/2,S=i.y+i.height/2-s.height/2,E=i[p]/2-s[p]/2;let A;switch(v){case"top":A={x:y,y:i.y-s.height};break;case"bottom":A={x:y,y:i.y+i.height};break;case"right":A={x:i.x+i.width,y:S};break;case"left":A={x:i.x-s.width,y:S};break;default:A={x:i.x,y:i.y}}switch(Ea(u)){case"start":A[m]-=E*(o&&h?-1:1);break;case"end":A[m]+=E*(o&&h?-1:1);break}return A}const iE=async(l,u,o)=>{const{placement:i="bottom",strategy:s="absolute",middleware:f=[],platform:m}=o,p=f.filter(Boolean),v=await(m.isRTL==null?void 0:m.isRTL(u));let h=await m.getElementRects({reference:l,floating:u,strategy:s}),{x:y,y:S}=Ap(h,i,v),E=i,A={},C=0;for(let R=0;R<p.length;R++){const{name:T,fn:N}=p[R],{x:H,y:k,data:X,reset:Y}=await N({x:y,y:S,initialPlacement:i,placement:E,strategy:s,middlewareData:A,rects:h,platform:m,elements:{reference:l,floating:u}});y=H??y,S=k??S,A={...A,[T]:{...A[T],...X}},Y&&C<=50&&(C++,typeof Y=="object"&&(Y.placement&&(E=Y.placement),Y.rects&&(h=Y.rects===!0?await m.getElementRects({reference:l,floating:u,strategy:s}):Y.rects),{x:y,y:S}=Ap(h,E,v)),R=-1)}return{x:y,y:S,placement:E,strategy:s,middlewareData:A}};async function Cr(l,u){var o;u===void 0&&(u={});const{x:i,y:s,platform:f,rects:m,elements:p,strategy:v}=l,{boundary:h="clippingAncestors",rootBoundary:y="viewport",elementContext:S="floating",altBoundary:E=!1,padding:A=0}=En(u,l),C=_v(A),T=p[E?S==="floating"?"reference":"floating":S],N=bi(await f.getClippingRect({element:(o=await(f.isElement==null?void 0:f.isElement(T)))==null||o?T:T.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(p.floating)),boundary:h,rootBoundary:y,strategy:v})),H=S==="floating"?{x:i,y:s,width:m.floating.width,height:m.floating.height}:m.reference,k=await(f.getOffsetParent==null?void 0:f.getOffsetParent(p.floating)),X=await(f.isElement==null?void 0:f.isElement(k))?await(f.getScale==null?void 0:f.getScale(k))||{x:1,y:1}:{x:1,y:1},Y=bi(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:p,rect:H,offsetParent:k,strategy:v}):H);return{top:(N.top-Y.top+C.top)/X.y,bottom:(Y.bottom-N.bottom+C.bottom)/X.y,left:(N.left-Y.left+C.left)/X.x,right:(Y.right-N.right+C.right)/X.x}}const oE=l=>({name:"arrow",options:l,async fn(u){const{x:o,y:i,placement:s,rects:f,platform:m,elements:p,middlewareData:v}=u,{element:h,padding:y=0}=En(l,u)||{};if(h==null)return{};const S=_v(y),E={x:o,y:i},A=Ys(s),C=Gs(A),R=await m.getDimensions(h),T=A==="y",N=T?"top":"left",H=T?"bottom":"right",k=T?"clientHeight":"clientWidth",X=f.reference[C]+f.reference[A]-E[A]-f.floating[C],Y=E[A]-f.reference[A],W=await(m.getOffsetParent==null?void 0:m.getOffsetParent(h));let $=W?W[k]:0;(!$||!await(m.isElement==null?void 0:m.isElement(W)))&&($=p.floating[k]||f.floating[C]);const Z=X/2-Y/2,le=$/2-R[C]/2-1,se=el(S[N],le),be=el(S[H],le),fe=se,Ee=$-R[C]-be,ge=$/2-R[C]/2+Z,me=xs(fe,ge,Ee),z=!v.arrow&&Ea(s)!=null&&ge!==me&&f.reference[C]/2-(ge<fe?se:be)-R[C]/2<0,K=z?ge<fe?ge-fe:ge-Ee:0;return{[A]:E[A]+K,data:{[A]:me,centerOffset:ge-me-K,...z&&{alignmentOffset:K}},reset:z}}}),cE=function(l){return l===void 0&&(l={}),{name:"flip",options:l,async fn(u){var o,i;const{placement:s,middlewareData:f,rects:m,initialPlacement:p,platform:v,elements:h}=u,{mainAxis:y=!0,crossAxis:S=!0,fallbackPlacements:E,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:R=!0,...T}=En(l,u);if((o=f.arrow)!=null&&o.alignmentOffset)return{};const N=wn(s),H=Jt(p),k=wn(p)===p,X=await(v.isRTL==null?void 0:v.isRTL(h.floating)),Y=E||(k||!R?[yi(p)]:tE(p)),W=C!=="none";!E&&W&&Y.push(...rE(p,R,C,X));const $=[p,...Y],Z=await Cr(u,T),le=[];let se=((i=f.flip)==null?void 0:i.overflows)||[];if(y&&le.push(Z[N]),S){const ge=eE(s,m,X);le.push(Z[ge[0]],Z[ge[1]])}if(se=[...se,{placement:s,overflows:le}],!le.every(ge=>ge<=0)){var be,fe;const ge=(((be=f.flip)==null?void 0:be.index)||0)+1,me=$[ge];if(me&&(!(S==="alignment"?H!==Jt(me):!1)||se.every(B=>Jt(B.placement)===H?B.overflows[0]>0:!0)))return{data:{index:ge,overflows:se},reset:{placement:me}};let z=(fe=se.filter(K=>K.overflows[0]<=0).sort((K,B)=>K.overflows[1]-B.overflows[1])[0])==null?void 0:fe.placement;if(!z)switch(A){case"bestFit":{var Ee;const K=(Ee=se.filter(B=>{if(W){const J=Jt(B.placement);return J===H||J==="y"}return!0}).map(B=>[B.placement,B.overflows.filter(J=>J>0).reduce((J,w)=>J+w,0)]).sort((B,J)=>B[1]-J[1])[0])==null?void 0:Ee[0];K&&(z=K);break}case"initialPlacement":z=p;break}if(s!==z)return{reset:{placement:z}}}return{}}}};function Mp(l,u){return{top:l.top-u.height,right:l.right-u.width,bottom:l.bottom-u.height,left:l.left-u.width}}function Tp(l){return Jx.some(u=>l[u]>=0)}const sE=function(l){return l===void 0&&(l={}),{name:"hide",options:l,async fn(u){const{rects:o}=u,{strategy:i="referenceHidden",...s}=En(l,u);switch(i){case"referenceHidden":{const f=await Cr(u,{...s,elementContext:"reference"}),m=Mp(f,o.reference);return{data:{referenceHiddenOffsets:m,referenceHidden:Tp(m)}}}case"escaped":{const f=await Cr(u,{...s,altBoundary:!0}),m=Mp(f,o.floating);return{data:{escapedOffsets:m,escaped:Tp(m)}}}default:return{}}}}},Dv=new Set(["left","top"]);async function fE(l,u){const{placement:o,platform:i,elements:s}=l,f=await(i.isRTL==null?void 0:i.isRTL(s.floating)),m=wn(o),p=Ea(o),v=Jt(o)==="y",h=Dv.has(m)?-1:1,y=f&&v?-1:1,S=En(u,l);let{mainAxis:E,crossAxis:A,alignmentAxis:C}=typeof S=="number"?{mainAxis:S,crossAxis:0,alignmentAxis:null}:{mainAxis:S.mainAxis||0,crossAxis:S.crossAxis||0,alignmentAxis:S.alignmentAxis};return p&&typeof C=="number"&&(A=p==="end"?C*-1:C),v?{x:A*y,y:E*h}:{x:E*h,y:A*y}}const dE=function(l){return l===void 0&&(l=0),{name:"offset",options:l,async fn(u){var o,i;const{x:s,y:f,placement:m,middlewareData:p}=u,v=await fE(u,l);return m===((o=p.offset)==null?void 0:o.placement)&&(i=p.arrow)!=null&&i.alignmentOffset?{}:{x:s+v.x,y:f+v.y,data:{...v,placement:m}}}}},mE=function(l){return l===void 0&&(l={}),{name:"shift",options:l,async fn(u){const{x:o,y:i,placement:s}=u,{mainAxis:f=!0,crossAxis:m=!1,limiter:p={fn:T=>{let{x:N,y:H}=T;return{x:N,y:H}}},...v}=En(l,u),h={x:o,y:i},y=await Cr(u,v),S=Jt(wn(s)),E=ks(S);let A=h[E],C=h[S];if(f){const T=E==="y"?"top":"left",N=E==="y"?"bottom":"right",H=A+y[T],k=A-y[N];A=xs(H,A,k)}if(m){const T=S==="y"?"top":"left",N=S==="y"?"bottom":"right",H=C+y[T],k=C-y[N];C=xs(H,C,k)}const R=p.fn({...u,[E]:A,[S]:C});return{...R,data:{x:R.x-o,y:R.y-i,enabled:{[E]:f,[S]:m}}}}}},hE=function(l){return l===void 0&&(l={}),{options:l,fn(u){const{x:o,y:i,placement:s,rects:f,middlewareData:m}=u,{offset:p=0,mainAxis:v=!0,crossAxis:h=!0}=En(l,u),y={x:o,y:i},S=Jt(s),E=ks(S);let A=y[E],C=y[S];const R=En(p,u),T=typeof R=="number"?{mainAxis:R,crossAxis:0}:{mainAxis:0,crossAxis:0,...R};if(v){const k=E==="y"?"height":"width",X=f.reference[E]-f.floating[k]+T.mainAxis,Y=f.reference[E]+f.reference[k]-T.mainAxis;A<X?A=X:A>Y&&(A=Y)}if(h){var N,H;const k=E==="y"?"width":"height",X=Dv.has(wn(s)),Y=f.reference[S]-f.floating[k]+(X&&((N=m.offset)==null?void 0:N[S])||0)+(X?0:T.crossAxis),W=f.reference[S]+f.reference[k]+(X?0:((H=m.offset)==null?void 0:H[S])||0)-(X?T.crossAxis:0);C<Y?C=Y:C>W&&(C=W)}return{[E]:A,[S]:C}}}},pE=function(l){return l===void 0&&(l={}),{name:"size",options:l,async fn(u){var o,i;const{placement:s,rects:f,platform:m,elements:p}=u,{apply:v=()=>{},...h}=En(l,u),y=await Cr(u,h),S=wn(s),E=Ea(s),A=Jt(s)==="y",{width:C,height:R}=f.floating;let T,N;S==="top"||S==="bottom"?(T=S,N=E===(await(m.isRTL==null?void 0:m.isRTL(p.floating))?"start":"end")?"left":"right"):(N=S,T=E==="end"?"top":"bottom");const H=R-y.top-y.bottom,k=C-y.left-y.right,X=el(R-y[T],H),Y=el(C-y[N],k),W=!u.middlewareData.shift;let $=X,Z=Y;if((o=u.middlewareData.shift)!=null&&o.enabled.x&&(Z=k),(i=u.middlewareData.shift)!=null&&i.enabled.y&&($=H),W&&!E){const se=wt(y.left,0),be=wt(y.right,0),fe=wt(y.top,0),Ee=wt(y.bottom,0);A?Z=C-2*(se!==0||be!==0?se+be:wt(y.left,y.right)):$=R-2*(fe!==0||Ee!==0?fe+Ee:wt(y.top,y.bottom))}await v({...u,availableWidth:Z,availableHeight:$});const le=await m.getDimensions(p.floating);return C!==le.width||R!==le.height?{reset:{rects:!0}}:{}}}};function Mi(){return typeof window<"u"}function wa(l){return Nv(l)?(l.nodeName||"").toLowerCase():"#document"}function Rt(l){var u;return(l==null||(u=l.ownerDocument)==null?void 0:u.defaultView)||window}function tn(l){var u;return(u=(Nv(l)?l.ownerDocument:l.document)||window.document)==null?void 0:u.documentElement}function Nv(l){return Mi()?l instanceof Node||l instanceof Rt(l).Node:!1}function Bt(l){return Mi()?l instanceof Element||l instanceof Rt(l).Element:!1}function It(l){return Mi()?l instanceof HTMLElement||l instanceof Rt(l).HTMLElement:!1}function Cp(l){return!Mi()||typeof ShadowRoot>"u"?!1:l instanceof ShadowRoot||l instanceof Rt(l).ShadowRoot}const vE=new Set(["inline","contents"]);function jr(l){const{overflow:u,overflowX:o,overflowY:i,display:s}=kt(l);return/auto|scroll|overlay|hidden|clip/.test(u+i+o)&&!vE.has(s)}const gE=new Set(["table","td","th"]);function yE(l){return gE.has(wa(l))}const bE=[":popover-open",":modal"];function Ti(l){return bE.some(u=>{try{return l.matches(u)}catch{return!1}})}const SE=["transform","translate","scale","rotate","perspective"],xE=["transform","translate","scale","rotate","perspective","filter"],EE=["paint","layout","strict","content"];function qs(l){const u=Vs(),o=Bt(l)?kt(l):l;return SE.some(i=>o[i]?o[i]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!u&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!u&&(o.filter?o.filter!=="none":!1)||xE.some(i=>(o.willChange||"").includes(i))||EE.some(i=>(o.contain||"").includes(i))}function wE(l){let u=tl(l);for(;It(u)&&!ga(u);){if(qs(u))return u;if(Ti(u))return null;u=tl(u)}return null}function Vs(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const RE=new Set(["html","body","#document"]);function ga(l){return RE.has(wa(l))}function kt(l){return Rt(l).getComputedStyle(l)}function Ci(l){return Bt(l)?{scrollLeft:l.scrollLeft,scrollTop:l.scrollTop}:{scrollLeft:l.scrollX,scrollTop:l.scrollY}}function tl(l){if(wa(l)==="html")return l;const u=l.assignedSlot||l.parentNode||Cp(l)&&l.host||tn(l);return Cp(u)?u.host:u}function zv(l){const u=tl(l);return ga(u)?l.ownerDocument?l.ownerDocument.body:l.body:It(u)&&jr(u)?u:zv(u)}function Or(l,u,o){var i;u===void 0&&(u=[]),o===void 0&&(o=!0);const s=zv(l),f=s===((i=l.ownerDocument)==null?void 0:i.body),m=Rt(s);if(f){const p=ws(m);return u.concat(m,m.visualViewport||[],jr(s)?s:[],p&&o?Or(p):[])}return u.concat(s,Or(s,[],o))}function ws(l){return l.parent&&Object.getPrototypeOf(l.parent)?l.frameElement:null}function Uv(l){const u=kt(l);let o=parseFloat(u.width)||0,i=parseFloat(u.height)||0;const s=It(l),f=s?l.offsetWidth:o,m=s?l.offsetHeight:i,p=gi(o)!==f||gi(i)!==m;return p&&(o=f,i=m),{width:o,height:i,$:p}}function Xs(l){return Bt(l)?l:l.contextElement}function pa(l){const u=Xs(l);if(!It(u))return Ft(1);const o=u.getBoundingClientRect(),{width:i,height:s,$:f}=Uv(u);let m=(f?gi(o.width):o.width)/i,p=(f?gi(o.height):o.height)/s;return(!m||!Number.isFinite(m))&&(m=1),(!p||!Number.isFinite(p))&&(p=1),{x:m,y:p}}const AE=Ft(0);function Lv(l){const u=Rt(l);return!Vs()||!u.visualViewport?AE:{x:u.visualViewport.offsetLeft,y:u.visualViewport.offsetTop}}function ME(l,u,o){return u===void 0&&(u=!1),!o||u&&o!==Rt(l)?!1:u}function xl(l,u,o,i){u===void 0&&(u=!1),o===void 0&&(o=!1);const s=l.getBoundingClientRect(),f=Xs(l);let m=Ft(1);u&&(i?Bt(i)&&(m=pa(i)):m=pa(l));const p=ME(f,o,i)?Lv(f):Ft(0);let v=(s.left+p.x)/m.x,h=(s.top+p.y)/m.y,y=s.width/m.x,S=s.height/m.y;if(f){const E=Rt(f),A=i&&Bt(i)?Rt(i):i;let C=E,R=ws(C);for(;R&&i&&A!==C;){const T=pa(R),N=R.getBoundingClientRect(),H=kt(R),k=N.left+(R.clientLeft+parseFloat(H.paddingLeft))*T.x,X=N.top+(R.clientTop+parseFloat(H.paddingTop))*T.y;v*=T.x,h*=T.y,y*=T.x,S*=T.y,v+=k,h+=X,C=Rt(R),R=ws(C)}}return bi({width:y,height:S,x:v,y:h})}function Oi(l,u){const o=Ci(l).scrollLeft;return u?u.left+o:xl(tn(l)).left+o}function jv(l,u){const o=l.getBoundingClientRect(),i=o.left+u.scrollLeft-Oi(l,o),s=o.top+u.scrollTop;return{x:i,y:s}}function TE(l){let{elements:u,rect:o,offsetParent:i,strategy:s}=l;const f=s==="fixed",m=tn(i),p=u?Ti(u.floating):!1;if(i===m||p&&f)return o;let v={scrollLeft:0,scrollTop:0},h=Ft(1);const y=Ft(0),S=It(i);if((S||!S&&!f)&&((wa(i)!=="body"||jr(m))&&(v=Ci(i)),It(i))){const A=xl(i);h=pa(i),y.x=A.x+i.clientLeft,y.y=A.y+i.clientTop}const E=m&&!S&&!f?jv(m,v):Ft(0);return{width:o.width*h.x,height:o.height*h.y,x:o.x*h.x-v.scrollLeft*h.x+y.x+E.x,y:o.y*h.y-v.scrollTop*h.y+y.y+E.y}}function CE(l){return Array.from(l.getClientRects())}function OE(l){const u=tn(l),o=Ci(l),i=l.ownerDocument.body,s=wt(u.scrollWidth,u.clientWidth,i.scrollWidth,i.clientWidth),f=wt(u.scrollHeight,u.clientHeight,i.scrollHeight,i.clientHeight);let m=-o.scrollLeft+Oi(l);const p=-o.scrollTop;return kt(i).direction==="rtl"&&(m+=wt(u.clientWidth,i.clientWidth)-s),{width:s,height:f,x:m,y:p}}const Op=25;function _E(l,u){const o=Rt(l),i=tn(l),s=o.visualViewport;let f=i.clientWidth,m=i.clientHeight,p=0,v=0;if(s){f=s.width,m=s.height;const y=Vs();(!y||y&&u==="fixed")&&(p=s.offsetLeft,v=s.offsetTop)}const h=Oi(i);if(h<=0){const y=i.ownerDocument,S=y.body,E=getComputedStyle(S),A=y.compatMode==="CSS1Compat"&&parseFloat(E.marginLeft)+parseFloat(E.marginRight)||0,C=Math.abs(i.clientWidth-S.clientWidth-A);C<=Op&&(f-=C)}else h<=Op&&(f+=h);return{width:f,height:m,x:p,y:v}}const DE=new Set(["absolute","fixed"]);function NE(l,u){const o=xl(l,!0,u==="fixed"),i=o.top+l.clientTop,s=o.left+l.clientLeft,f=It(l)?pa(l):Ft(1),m=l.clientWidth*f.x,p=l.clientHeight*f.y,v=s*f.x,h=i*f.y;return{width:m,height:p,x:v,y:h}}function _p(l,u,o){let i;if(u==="viewport")i=_E(l,o);else if(u==="document")i=OE(tn(l));else if(Bt(u))i=NE(u,o);else{const s=Lv(l);i={x:u.x-s.x,y:u.y-s.y,width:u.width,height:u.height}}return bi(i)}function Hv(l,u){const o=tl(l);return o===u||!Bt(o)||ga(o)?!1:kt(o).position==="fixed"||Hv(o,u)}function zE(l,u){const o=u.get(l);if(o)return o;let i=Or(l,[],!1).filter(p=>Bt(p)&&wa(p)!=="body"),s=null;const f=kt(l).position==="fixed";let m=f?tl(l):l;for(;Bt(m)&&!ga(m);){const p=kt(m),v=qs(m);!v&&p.position==="fixed"&&(s=null),(f?!v&&!s:!v&&p.position==="static"&&!!s&&DE.has(s.position)||jr(m)&&!v&&Hv(l,m))?i=i.filter(y=>y!==m):s=p,m=tl(m)}return u.set(l,i),i}function UE(l){let{element:u,boundary:o,rootBoundary:i,strategy:s}=l;const m=[...o==="clippingAncestors"?Ti(u)?[]:zE(u,this._c):[].concat(o),i],p=m[0],v=m.reduce((h,y)=>{const S=_p(u,y,s);return h.top=wt(S.top,h.top),h.right=el(S.right,h.right),h.bottom=el(S.bottom,h.bottom),h.left=wt(S.left,h.left),h},_p(u,p,s));return{width:v.right-v.left,height:v.bottom-v.top,x:v.left,y:v.top}}function LE(l){const{width:u,height:o}=Uv(l);return{width:u,height:o}}function jE(l,u,o){const i=It(u),s=tn(u),f=o==="fixed",m=xl(l,!0,f,u);let p={scrollLeft:0,scrollTop:0};const v=Ft(0);function h(){v.x=Oi(s)}if(i||!i&&!f)if((wa(u)!=="body"||jr(s))&&(p=Ci(u)),i){const A=xl(u,!0,f,u);v.x=A.x+u.clientLeft,v.y=A.y+u.clientTop}else s&&h();f&&!i&&s&&h();const y=s&&!i&&!f?jv(s,p):Ft(0),S=m.left+p.scrollLeft-v.x-y.x,E=m.top+p.scrollTop-v.y-y.y;return{x:S,y:E,width:m.width,height:m.height}}function us(l){return kt(l).position==="static"}function Dp(l,u){if(!It(l)||kt(l).position==="fixed")return null;if(u)return u(l);let o=l.offsetParent;return tn(l)===o&&(o=o.ownerDocument.body),o}function Bv(l,u){const o=Rt(l);if(Ti(l))return o;if(!It(l)){let s=tl(l);for(;s&&!ga(s);){if(Bt(s)&&!us(s))return s;s=tl(s)}return o}let i=Dp(l,u);for(;i&&yE(i)&&us(i);)i=Dp(i,u);return i&&ga(i)&&us(i)&&!qs(i)?o:i||wE(l)||o}const HE=async function(l){const u=this.getOffsetParent||Bv,o=this.getDimensions,i=await o(l.floating);return{reference:jE(l.reference,await u(l.floating),l.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}};function BE(l){return kt(l).direction==="rtl"}const kE={convertOffsetParentRelativeRectToViewportRelativeRect:TE,getDocumentElement:tn,getClippingRect:UE,getOffsetParent:Bv,getElementRects:HE,getClientRects:CE,getDimensions:LE,getScale:pa,isElement:Bt,isRTL:BE};function kv(l,u){return l.x===u.x&&l.y===u.y&&l.width===u.width&&l.height===u.height}function GE(l,u){let o=null,i;const s=tn(l);function f(){var p;clearTimeout(i),(p=o)==null||p.disconnect(),o=null}function m(p,v){p===void 0&&(p=!1),v===void 0&&(v=1),f();const h=l.getBoundingClientRect(),{left:y,top:S,width:E,height:A}=h;if(p||u(),!E||!A)return;const C=ai(S),R=ai(s.clientWidth-(y+E)),T=ai(s.clientHeight-(S+A)),N=ai(y),k={rootMargin:-C+"px "+-R+"px "+-T+"px "+-N+"px",threshold:wt(0,el(1,v))||1};let X=!0;function Y(W){const $=W[0].intersectionRatio;if($!==v){if(!X)return m();$?m(!1,$):i=setTimeout(()=>{m(!1,1e-7)},1e3)}$===1&&!kv(h,l.getBoundingClientRect())&&m(),X=!1}try{o=new IntersectionObserver(Y,{...k,root:s.ownerDocument})}catch{o=new IntersectionObserver(Y,k)}o.observe(l)}return m(!0),f}function YE(l,u,o,i){i===void 0&&(i={});const{ancestorScroll:s=!0,ancestorResize:f=!0,elementResize:m=typeof ResizeObserver=="function",layoutShift:p=typeof IntersectionObserver=="function",animationFrame:v=!1}=i,h=Xs(l),y=s||f?[...h?Or(h):[],...Or(u)]:[];y.forEach(N=>{s&&N.addEventListener("scroll",o,{passive:!0}),f&&N.addEventListener("resize",o)});const S=h&&p?GE(h,o):null;let E=-1,A=null;m&&(A=new ResizeObserver(N=>{let[H]=N;H&&H.target===h&&A&&(A.unobserve(u),cancelAnimationFrame(E),E=requestAnimationFrame(()=>{var k;(k=A)==null||k.observe(u)})),o()}),h&&!v&&A.observe(h),A.observe(u));let C,R=v?xl(l):null;v&&T();function T(){const N=xl(l);R&&!kv(R,N)&&o(),R=N,C=requestAnimationFrame(T)}return o(),()=>{var N;y.forEach(H=>{s&&H.removeEventListener("scroll",o),f&&H.removeEventListener("resize",o)}),S?.(),(N=A)==null||N.disconnect(),A=null,v&&cancelAnimationFrame(C)}}const qE=dE,VE=mE,XE=cE,QE=pE,ZE=sE,Np=oE,KE=hE,PE=(l,u,o)=>{const i=new Map,s={platform:kE,...o},f={...s.platform,_c:i};return iE(l,u,{...s,platform:f})};var $E=typeof document<"u",JE=function(){},di=$E?g.useLayoutEffect:JE;function Si(l,u){if(l===u)return!0;if(typeof l!=typeof u)return!1;if(typeof l=="function"&&l.toString()===u.toString())return!0;let o,i,s;if(l&&u&&typeof l=="object"){if(Array.isArray(l)){if(o=l.length,o!==u.length)return!1;for(i=o;i--!==0;)if(!Si(l[i],u[i]))return!1;return!0}if(s=Object.keys(l),o=s.length,o!==Object.keys(u).length)return!1;for(i=o;i--!==0;)if(!{}.hasOwnProperty.call(u,s[i]))return!1;for(i=o;i--!==0;){const f=s[i];if(!(f==="_owner"&&l.$$typeof)&&!Si(l[f],u[f]))return!1}return!0}return l!==l&&u!==u}function Gv(l){return typeof window>"u"?1:(l.ownerDocument.defaultView||window).devicePixelRatio||1}function zp(l,u){const o=Gv(l);return Math.round(u*o)/o}function is(l){const u=g.useRef(l);return di(()=>{u.current=l}),u}function FE(l){l===void 0&&(l={});const{placement:u="bottom",strategy:o="absolute",middleware:i=[],platform:s,elements:{reference:f,floating:m}={},transform:p=!0,whileElementsMounted:v,open:h}=l,[y,S]=g.useState({x:0,y:0,strategy:o,placement:u,middlewareData:{},isPositioned:!1}),[E,A]=g.useState(i);Si(E,i)||A(i);const[C,R]=g.useState(null),[T,N]=g.useState(null),H=g.useCallback(B=>{B!==W.current&&(W.current=B,R(B))},[]),k=g.useCallback(B=>{B!==$.current&&($.current=B,N(B))},[]),X=f||C,Y=m||T,W=g.useRef(null),$=g.useRef(null),Z=g.useRef(y),le=v!=null,se=is(v),be=is(s),fe=is(h),Ee=g.useCallback(()=>{if(!W.current||!$.current)return;const B={placement:u,strategy:o,middleware:E};be.current&&(B.platform=be.current),PE(W.current,$.current,B).then(J=>{const w={...J,isPositioned:fe.current!==!1};ge.current&&!Si(Z.current,w)&&(Z.current=w,Ls.flushSync(()=>{S(w)}))})},[E,u,o,be,fe]);di(()=>{h===!1&&Z.current.isPositioned&&(Z.current.isPositioned=!1,S(B=>({...B,isPositioned:!1})))},[h]);const ge=g.useRef(!1);di(()=>(ge.current=!0,()=>{ge.current=!1}),[]),di(()=>{if(X&&(W.current=X),Y&&($.current=Y),X&&Y){if(se.current)return se.current(X,Y,Ee);Ee()}},[X,Y,Ee,se,le]);const me=g.useMemo(()=>({reference:W,floating:$,setReference:H,setFloating:k}),[H,k]),z=g.useMemo(()=>({reference:X,floating:Y}),[X,Y]),K=g.useMemo(()=>{const B={position:o,left:0,top:0};if(!z.floating)return B;const J=zp(z.floating,y.x),w=zp(z.floating,y.y);return p?{...B,transform:"translate("+J+"px, "+w+"px)",...Gv(z.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:J,top:w}},[o,p,z.floating,y.x,y.y]);return g.useMemo(()=>({...y,update:Ee,refs:me,elements:z,floatingStyles:K}),[y,Ee,me,z,K])}const WE=l=>{function u(o){return{}.hasOwnProperty.call(o,"current")}return{name:"arrow",options:l,fn(o){const{element:i,padding:s}=typeof l=="function"?l(o):l;return i&&u(i)?i.current!=null?Np({element:i.current,padding:s}).fn(o):{}:i?Np({element:i,padding:s}).fn(o):{}}}},IE=(l,u)=>({...qE(l),options:[l,u]}),ew=(l,u)=>({...VE(l),options:[l,u]}),tw=(l,u)=>({...KE(l),options:[l,u]}),nw=(l,u)=>({...XE(l),options:[l,u]}),lw=(l,u)=>({...QE(l),options:[l,u]}),aw=(l,u)=>({...ZE(l),options:[l,u]}),rw=(l,u)=>({...WE(l),options:[l,u]});var uw="Arrow",Yv=g.forwardRef((l,u)=>{const{children:o,width:i=10,height:s=5,...f}=l;return _.jsx(ut.svg,{...f,ref:u,width:i,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:l.asChild?o:_.jsx("polygon",{points:"0,0 30,0 15,10"})})});Yv.displayName=uw;var iw=Yv;function ow(l){const[u,o]=g.useState(void 0);return In(()=>{if(l){o({width:l.offsetWidth,height:l.offsetHeight});const i=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const f=s[0];let m,p;if("borderBoxSize"in f){const v=f.borderBoxSize,h=Array.isArray(v)?v[0]:v;m=h.inlineSize,p=h.blockSize}else m=l.offsetWidth,p=l.offsetHeight;o({width:m,height:p})});return i.observe(l,{box:"border-box"}),()=>i.unobserve(l)}else o(void 0)},[l]),u}var Qs="Popper",[qv,Vv]=Lr(Qs),[cw,Xv]=qv(Qs),Qv=l=>{const{__scopePopper:u,children:o}=l,[i,s]=g.useState(null);return _.jsx(cw,{scope:u,anchor:i,onAnchorChange:s,children:o})};Qv.displayName=Qs;var Zv="PopperAnchor",Kv=g.forwardRef((l,u)=>{const{__scopePopper:o,virtualRef:i,...s}=l,f=Xv(Zv,o),m=g.useRef(null),p=mt(u,m),v=g.useRef(null);return g.useEffect(()=>{const h=v.current;v.current=i?.current||m.current,h!==v.current&&f.onAnchorChange(v.current)}),i?null:_.jsx(ut.div,{...s,ref:p})});Kv.displayName=Zv;var Zs="PopperContent",[sw,fw]=qv(Zs),Pv=g.forwardRef((l,u)=>{const{__scopePopper:o,side:i="bottom",sideOffset:s=0,align:f="center",alignOffset:m=0,arrowPadding:p=0,avoidCollisions:v=!0,collisionBoundary:h=[],collisionPadding:y=0,sticky:S="partial",hideWhenDetached:E=!1,updatePositionStrategy:A="optimized",onPlaced:C,...R}=l,T=Xv(Zs,o),[N,H]=g.useState(null),k=mt(u,ue=>H(ue)),[X,Y]=g.useState(null),W=ow(X),$=W?.width??0,Z=W?.height??0,le=i+(f!=="center"?"-"+f:""),se=typeof y=="number"?y:{top:0,right:0,bottom:0,left:0,...y},be=Array.isArray(h)?h:[h],fe=be.length>0,Ee={padding:se,boundary:be.filter(mw),altBoundary:fe},{refs:ge,floatingStyles:me,placement:z,isPositioned:K,middlewareData:B}=FE({strategy:"fixed",placement:le,whileElementsMounted:(...ue)=>YE(...ue,{animationFrame:A==="always"}),elements:{reference:T.anchor},middleware:[IE({mainAxis:s+Z,alignmentAxis:m}),v&&ew({mainAxis:!0,crossAxis:!1,limiter:S==="partial"?tw():void 0,...Ee}),v&&nw({...Ee}),lw({...Ee,apply:({elements:ue,rects:Ae,availableWidth:at,availableHeight:Gt})=>{const{width:Yt,height:qt}=Ae.reference,An=ue.floating.style;An.setProperty("--radix-popper-available-width",`${at}px`),An.setProperty("--radix-popper-available-height",`${Gt}px`),An.setProperty("--radix-popper-anchor-width",`${Yt}px`),An.setProperty("--radix-popper-anchor-height",`${qt}px`)}}),X&&rw({element:X,padding:p}),hw({arrowWidth:$,arrowHeight:Z}),E&&aw({strategy:"referenceHidden",...Ee})]}),[J,w]=Fv(z),V=xn(C);In(()=>{K&&V?.()},[K,V]);const F=B.arrow?.x,P=B.arrow?.y,I=B.arrow?.centerOffset!==0,[de,re]=g.useState();return In(()=>{N&&re(window.getComputedStyle(N).zIndex)},[N]),_.jsx("div",{ref:ge.setFloating,"data-radix-popper-content-wrapper":"",style:{...me,transform:K?me.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:de,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:l.dir,children:_.jsx(sw,{scope:o,placedSide:J,onArrowChange:Y,arrowX:F,arrowY:P,shouldHideArrow:I,children:_.jsx(ut.div,{"data-side":J,"data-align":w,...R,ref:k,style:{...R.style,animation:K?void 0:"none"}})})})});Pv.displayName=Zs;var $v="PopperArrow",dw={top:"bottom",right:"left",bottom:"top",left:"right"},Jv=g.forwardRef(function(u,o){const{__scopePopper:i,...s}=u,f=fw($v,i),m=dw[f.placedSide];return _.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[m]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:_.jsx(iw,{...s,ref:o,style:{...s.style,display:"block"}})})});Jv.displayName=$v;function mw(l){return l!==null}var hw=l=>({name:"transformOrigin",options:l,fn(u){const{placement:o,rects:i,middlewareData:s}=u,m=s.arrow?.centerOffset!==0,p=m?0:l.arrowWidth,v=m?0:l.arrowHeight,[h,y]=Fv(o),S={start:"0%",center:"50%",end:"100%"}[y],E=(s.arrow?.x??0)+p/2,A=(s.arrow?.y??0)+v/2;let C="",R="";return h==="bottom"?(C=m?S:`${E}px`,R=`${-v}px`):h==="top"?(C=m?S:`${E}px`,R=`${i.floating.height+v}px`):h==="right"?(C=`${-v}px`,R=m?S:`${A}px`):h==="left"&&(C=`${i.floating.width+v}px`,R=m?S:`${A}px`),{data:{x:C,y:R}}}});function Fv(l){const[u,o="center"]=l.split("-");return[u,o]}var pw=Qv,vw=Kv,gw=Pv,yw=Jv,bw="Portal",Wv=g.forwardRef((l,u)=>{const{container:o,...i}=l,[s,f]=g.useState(!1);In(()=>f(!0),[]);const m=o||s&&globalThis?.document?.body;return m?b1.createPortal(_.jsx(ut.div,{...i,ref:u}),m):null});Wv.displayName=bw;function Sw(l,u){return g.useReducer((o,i)=>u[o][i]??o,l)}var Hr=l=>{const{present:u,children:o}=l,i=xw(u),s=typeof o=="function"?o({present:i.isPresent}):g.Children.only(o),f=mt(i.ref,Ew(s));return typeof o=="function"||i.isPresent?g.cloneElement(s,{ref:f}):null};Hr.displayName="Presence";function xw(l){const[u,o]=g.useState(),i=g.useRef(null),s=g.useRef(l),f=g.useRef("none"),m=l?"mounted":"unmounted",[p,v]=Sw(m,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const h=ri(i.current);f.current=p==="mounted"?h:"none"},[p]),In(()=>{const h=i.current,y=s.current;if(y!==l){const E=f.current,A=ri(h);l?v("MOUNT"):A==="none"||h?.display==="none"?v("UNMOUNT"):v(y&&E!==A?"ANIMATION_OUT":"UNMOUNT"),s.current=l}},[l,v]),In(()=>{if(u){let h;const y=u.ownerDocument.defaultView??window,S=A=>{const R=ri(i.current).includes(CSS.escape(A.animationName));if(A.target===u&&R&&(v("ANIMATION_END"),!s.current)){const T=u.style.animationFillMode;u.style.animationFillMode="forwards",h=y.setTimeout(()=>{u.style.animationFillMode==="forwards"&&(u.style.animationFillMode=T)})}},E=A=>{A.target===u&&(f.current=ri(i.current))};return u.addEventListener("animationstart",E),u.addEventListener("animationcancel",S),u.addEventListener("animationend",S),()=>{y.clearTimeout(h),u.removeEventListener("animationstart",E),u.removeEventListener("animationcancel",S),u.removeEventListener("animationend",S)}}else v("ANIMATION_END")},[u,v]),{isPresent:["mounted","unmountSuspended"].includes(p),ref:g.useCallback(h=>{i.current=h?getComputedStyle(h):null,o(h)},[])}}function ri(l){return l?.animationName||"none"}function Ew(l){let u=Object.getOwnPropertyDescriptor(l.props,"ref")?.get,o=u&&"isReactWarning"in u&&u.isReactWarning;return o?l.ref:(u=Object.getOwnPropertyDescriptor(l,"ref")?.get,o=u&&"isReactWarning"in u&&u.isReactWarning,o?l.props.ref:l.props.ref||l.ref)}var os="rovingFocusGroup.onEntryFocus",ww={bubbles:!1,cancelable:!0},Br="RovingFocusGroup",[Rs,Iv,Rw]=wv(Br),[Aw,eg]=Lr(Br,[Rw]),[Mw,Tw]=Aw(Br),tg=g.forwardRef((l,u)=>_.jsx(Rs.Provider,{scope:l.__scopeRovingFocusGroup,children:_.jsx(Rs.Slot,{scope:l.__scopeRovingFocusGroup,children:_.jsx(Cw,{...l,ref:u})})}));tg.displayName=Br;var Cw=g.forwardRef((l,u)=>{const{__scopeRovingFocusGroup:o,orientation:i,loop:s=!1,dir:f,currentTabStopId:m,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:v,onEntryFocus:h,preventScrollOnEntryFocus:y=!1,...S}=l,E=g.useRef(null),A=mt(u,E),C=Rv(f),[R,T]=xv({prop:m,defaultProp:p??null,onChange:v,caller:Br}),[N,H]=g.useState(!1),k=xn(h),X=Iv(o),Y=g.useRef(!1),[W,$]=g.useState(0);return g.useEffect(()=>{const Z=E.current;if(Z)return Z.addEventListener(os,k),()=>Z.removeEventListener(os,k)},[k]),_.jsx(Mw,{scope:o,orientation:i,dir:C,loop:s,currentTabStopId:R,onItemFocus:g.useCallback(Z=>T(Z),[T]),onItemShiftTab:g.useCallback(()=>H(!0),[]),onFocusableItemAdd:g.useCallback(()=>$(Z=>Z+1),[]),onFocusableItemRemove:g.useCallback(()=>$(Z=>Z-1),[]),children:_.jsx(ut.div,{tabIndex:N||W===0?-1:0,"data-orientation":i,...S,ref:A,style:{outline:"none",...l.style},onMouseDown:Re(l.onMouseDown,()=>{Y.current=!0}),onFocus:Re(l.onFocus,Z=>{const le=!Y.current;if(Z.target===Z.currentTarget&&le&&!N){const se=new CustomEvent(os,ww);if(Z.currentTarget.dispatchEvent(se),!se.defaultPrevented){const be=X().filter(z=>z.focusable),fe=be.find(z=>z.active),Ee=be.find(z=>z.id===R),me=[fe,Ee,...be].filter(Boolean).map(z=>z.ref.current);ag(me,y)}}Y.current=!1}),onBlur:Re(l.onBlur,()=>H(!1))})})}),ng="RovingFocusGroupItem",lg=g.forwardRef((l,u)=>{const{__scopeRovingFocusGroup:o,focusable:i=!0,active:s=!1,tabStopId:f,children:m,...p}=l,v=Ss(),h=f||v,y=Tw(ng,o),S=y.currentTabStopId===h,E=Iv(o),{onFocusableItemAdd:A,onFocusableItemRemove:C,currentTabStopId:R}=y;return g.useEffect(()=>{if(i)return A(),()=>C()},[i,A,C]),_.jsx(Rs.ItemSlot,{scope:o,id:h,focusable:i,active:s,children:_.jsx(ut.span,{tabIndex:S?0:-1,"data-orientation":y.orientation,...p,ref:u,onMouseDown:Re(l.onMouseDown,T=>{i?y.onItemFocus(h):T.preventDefault()}),onFocus:Re(l.onFocus,()=>y.onItemFocus(h)),onKeyDown:Re(l.onKeyDown,T=>{if(T.key==="Tab"&&T.shiftKey){y.onItemShiftTab();return}if(T.target!==T.currentTarget)return;const N=Dw(T,y.orientation,y.dir);if(N!==void 0){if(T.metaKey||T.ctrlKey||T.altKey||T.shiftKey)return;T.preventDefault();let k=E().filter(X=>X.focusable).map(X=>X.ref.current);if(N==="last")k.reverse();else if(N==="prev"||N==="next"){N==="prev"&&k.reverse();const X=k.indexOf(T.currentTarget);k=y.loop?Nw(k,X+1):k.slice(X+1)}setTimeout(()=>ag(k))}}),children:typeof m=="function"?m({isCurrentTabStop:S,hasTabStop:R!=null}):m})})});lg.displayName=ng;var Ow={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _w(l,u){return u!=="rtl"?l:l==="ArrowLeft"?"ArrowRight":l==="ArrowRight"?"ArrowLeft":l}function Dw(l,u,o){const i=_w(l.key,o);if(!(u==="vertical"&&["ArrowLeft","ArrowRight"].includes(i))&&!(u==="horizontal"&&["ArrowUp","ArrowDown"].includes(i)))return Ow[i]}function ag(l,u=!1){const o=document.activeElement;for(const i of l)if(i===o||(i.focus({preventScroll:u}),document.activeElement!==o))return}function Nw(l,u){return l.map((o,i)=>l[(u+i)%l.length])}var zw=tg,Uw=lg,Lw=function(l){if(typeof document>"u")return null;var u=Array.isArray(l)?l[0]:l;return u.ownerDocument.body},da=new WeakMap,ui=new WeakMap,ii={},cs=0,rg=function(l){return l&&(l.host||rg(l.parentNode))},jw=function(l,u){return u.map(function(o){if(l.contains(o))return o;var i=rg(o);return i&&l.contains(i)?i:(console.error("aria-hidden",o,"in not contained inside",l,". Doing nothing"),null)}).filter(function(o){return!!o})},Hw=function(l,u,o,i){var s=jw(u,Array.isArray(l)?l:[l]);ii[o]||(ii[o]=new WeakMap);var f=ii[o],m=[],p=new Set,v=new Set(s),h=function(S){!S||p.has(S)||(p.add(S),h(S.parentNode))};s.forEach(h);var y=function(S){!S||v.has(S)||Array.prototype.forEach.call(S.children,function(E){if(p.has(E))y(E);else try{var A=E.getAttribute(i),C=A!==null&&A!=="false",R=(da.get(E)||0)+1,T=(f.get(E)||0)+1;da.set(E,R),f.set(E,T),m.push(E),R===1&&C&&ui.set(E,!0),T===1&&E.setAttribute(o,"true"),C||E.setAttribute(i,"true")}catch(N){console.error("aria-hidden: cannot operate on ",E,N)}})};return y(u),p.clear(),cs++,function(){m.forEach(function(S){var E=da.get(S)-1,A=f.get(S)-1;da.set(S,E),f.set(S,A),E||(ui.has(S)||S.removeAttribute(i),ui.delete(S)),A||S.removeAttribute(o)}),cs--,cs||(da=new WeakMap,da=new WeakMap,ui=new WeakMap,ii={})}},Bw=function(l,u,o){o===void 0&&(o="data-aria-hidden");var i=Array.from(Array.isArray(l)?l:[l]),s=Lw(l);return s?(i.push.apply(i,Array.from(s.querySelectorAll("[aria-live], script"))),Hw(i,s,o,"aria-hidden")):function(){return null}},$t=function(){return $t=Object.assign||function(u){for(var o,i=1,s=arguments.length;i<s;i++){o=arguments[i];for(var f in o)Object.prototype.hasOwnProperty.call(o,f)&&(u[f]=o[f])}return u},$t.apply(this,arguments)};function ug(l,u){var o={};for(var i in l)Object.prototype.hasOwnProperty.call(l,i)&&u.indexOf(i)<0&&(o[i]=l[i]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,i=Object.getOwnPropertySymbols(l);s<i.length;s++)u.indexOf(i[s])<0&&Object.prototype.propertyIsEnumerable.call(l,i[s])&&(o[i[s]]=l[i[s]]);return o}function kw(l,u,o){if(o||arguments.length===2)for(var i=0,s=u.length,f;i<s;i++)(f||!(i in u))&&(f||(f=Array.prototype.slice.call(u,0,i)),f[i]=u[i]);return l.concat(f||Array.prototype.slice.call(u))}var mi="right-scroll-bar-position",hi="width-before-scroll-bar",Gw="with-scroll-bars-hidden",Yw="--removed-body-scroll-bar-size";function ss(l,u){return typeof l=="function"?l(u):l&&(l.current=u),l}function qw(l,u){var o=g.useState(function(){return{value:l,callback:u,facade:{get current(){return o.value},set current(i){var s=o.value;s!==i&&(o.value=i,o.callback(i,s))}}}})[0];return o.callback=u,o.facade}var Vw=typeof window<"u"?g.useLayoutEffect:g.useEffect,Up=new WeakMap;function Xw(l,u){var o=qw(null,function(i){return l.forEach(function(s){return ss(s,i)})});return Vw(function(){var i=Up.get(o);if(i){var s=new Set(i),f=new Set(l),m=o.current;s.forEach(function(p){f.has(p)||ss(p,null)}),f.forEach(function(p){s.has(p)||ss(p,m)})}Up.set(o,l)},[l]),o}function Qw(l){return l}function Zw(l,u){u===void 0&&(u=Qw);var o=[],i=!1,s={read:function(){if(i)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return o.length?o[o.length-1]:l},useMedium:function(f){var m=u(f,i);return o.push(m),function(){o=o.filter(function(p){return p!==m})}},assignSyncMedium:function(f){for(i=!0;o.length;){var m=o;o=[],m.forEach(f)}o={push:function(p){return f(p)},filter:function(){return o}}},assignMedium:function(f){i=!0;var m=[];if(o.length){var p=o;o=[],p.forEach(f),m=o}var v=function(){var y=m;m=[],y.forEach(f)},h=function(){return Promise.resolve().then(v)};h(),o={push:function(y){m.push(y),h()},filter:function(y){return m=m.filter(y),o}}}};return s}function Kw(l){l===void 0&&(l={});var u=Zw(null);return u.options=$t({async:!0,ssr:!1},l),u}var ig=function(l){var u=l.sideCar,o=ug(l,["sideCar"]);if(!u)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var i=u.read();if(!i)throw new Error("Sidecar medium not found");return g.createElement(i,$t({},o))};ig.isSideCarExport=!0;function Pw(l,u){return l.useMedium(u),ig}var og=Kw(),fs=function(){},_i=g.forwardRef(function(l,u){var o=g.useRef(null),i=g.useState({onScrollCapture:fs,onWheelCapture:fs,onTouchMoveCapture:fs}),s=i[0],f=i[1],m=l.forwardProps,p=l.children,v=l.className,h=l.removeScrollBar,y=l.enabled,S=l.shards,E=l.sideCar,A=l.noRelative,C=l.noIsolation,R=l.inert,T=l.allowPinchZoom,N=l.as,H=N===void 0?"div":N,k=l.gapMode,X=ug(l,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),Y=E,W=Xw([o,u]),$=$t($t({},X),s);return g.createElement(g.Fragment,null,y&&g.createElement(Y,{sideCar:og,removeScrollBar:h,shards:S,noRelative:A,noIsolation:C,inert:R,setCallbacks:f,allowPinchZoom:!!T,lockRef:o,gapMode:k}),m?g.cloneElement(g.Children.only(p),$t($t({},$),{ref:W})):g.createElement(H,$t({},$,{className:v,ref:W}),p))});_i.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};_i.classNames={fullWidth:hi,zeroRight:mi};var $w=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Jw(){if(!document)return null;var l=document.createElement("style");l.type="text/css";var u=$w();return u&&l.setAttribute("nonce",u),l}function Fw(l,u){l.styleSheet?l.styleSheet.cssText=u:l.appendChild(document.createTextNode(u))}function Ww(l){var u=document.head||document.getElementsByTagName("head")[0];u.appendChild(l)}var Iw=function(){var l=0,u=null;return{add:function(o){l==0&&(u=Jw())&&(Fw(u,o),Ww(u)),l++},remove:function(){l--,!l&&u&&(u.parentNode&&u.parentNode.removeChild(u),u=null)}}},eR=function(){var l=Iw();return function(u,o){g.useEffect(function(){return l.add(u),function(){l.remove()}},[u&&o])}},cg=function(){var l=eR(),u=function(o){var i=o.styles,s=o.dynamic;return l(i,s),null};return u},tR={left:0,top:0,right:0,gap:0},ds=function(l){return parseInt(l||"",10)||0},nR=function(l){var u=window.getComputedStyle(document.body),o=u[l==="padding"?"paddingLeft":"marginLeft"],i=u[l==="padding"?"paddingTop":"marginTop"],s=u[l==="padding"?"paddingRight":"marginRight"];return[ds(o),ds(i),ds(s)]},lR=function(l){if(l===void 0&&(l="margin"),typeof window>"u")return tR;var u=nR(l),o=document.documentElement.clientWidth,i=window.innerWidth;return{left:u[0],top:u[1],right:u[2],gap:Math.max(0,i-o+u[2]-u[0])}},aR=cg(),va="data-scroll-locked",rR=function(l,u,o,i){var s=l.left,f=l.top,m=l.right,p=l.gap;return o===void 0&&(o="margin"),`
  .`.concat(Gw,` {
   overflow: hidden `).concat(i,`;
   padding-right: `).concat(p,"px ").concat(i,`;
  }
  body[`).concat(va,`] {
    overflow: hidden `).concat(i,`;
    overscroll-behavior: contain;
    `).concat([u&&"position: relative ".concat(i,";"),o==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(f,`px;
    padding-right: `).concat(m,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(p,"px ").concat(i,`;
    `),o==="padding"&&"padding-right: ".concat(p,"px ").concat(i,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(mi,` {
    right: `).concat(p,"px ").concat(i,`;
  }
  
  .`).concat(hi,` {
    margin-right: `).concat(p,"px ").concat(i,`;
  }
  
  .`).concat(mi," .").concat(mi,` {
    right: 0 `).concat(i,`;
  }
  
  .`).concat(hi," .").concat(hi,` {
    margin-right: 0 `).concat(i,`;
  }
  
  body[`).concat(va,`] {
    `).concat(Yw,": ").concat(p,`px;
  }
`)},Lp=function(){var l=parseInt(document.body.getAttribute(va)||"0",10);return isFinite(l)?l:0},uR=function(){g.useEffect(function(){return document.body.setAttribute(va,(Lp()+1).toString()),function(){var l=Lp()-1;l<=0?document.body.removeAttribute(va):document.body.setAttribute(va,l.toString())}},[])},iR=function(l){var u=l.noRelative,o=l.noImportant,i=l.gapMode,s=i===void 0?"margin":i;uR();var f=g.useMemo(function(){return lR(s)},[s]);return g.createElement(aR,{styles:rR(f,!u,s,o?"":"!important")})},As=!1;if(typeof window<"u")try{var oi=Object.defineProperty({},"passive",{get:function(){return As=!0,!0}});window.addEventListener("test",oi,oi),window.removeEventListener("test",oi,oi)}catch{As=!1}var ma=As?{passive:!1}:!1,oR=function(l){return l.tagName==="TEXTAREA"},sg=function(l,u){if(!(l instanceof Element))return!1;var o=window.getComputedStyle(l);return o[u]!=="hidden"&&!(o.overflowY===o.overflowX&&!oR(l)&&o[u]==="visible")},cR=function(l){return sg(l,"overflowY")},sR=function(l){return sg(l,"overflowX")},jp=function(l,u){var o=u.ownerDocument,i=u;do{typeof ShadowRoot<"u"&&i instanceof ShadowRoot&&(i=i.host);var s=fg(l,i);if(s){var f=dg(l,i),m=f[1],p=f[2];if(m>p)return!0}i=i.parentNode}while(i&&i!==o.body);return!1},fR=function(l){var u=l.scrollTop,o=l.scrollHeight,i=l.clientHeight;return[u,o,i]},dR=function(l){var u=l.scrollLeft,o=l.scrollWidth,i=l.clientWidth;return[u,o,i]},fg=function(l,u){return l==="v"?cR(u):sR(u)},dg=function(l,u){return l==="v"?fR(u):dR(u)},mR=function(l,u){return l==="h"&&u==="rtl"?-1:1},hR=function(l,u,o,i,s){var f=mR(l,window.getComputedStyle(u).direction),m=f*i,p=o.target,v=u.contains(p),h=!1,y=m>0,S=0,E=0;do{if(!p)break;var A=dg(l,p),C=A[0],R=A[1],T=A[2],N=R-T-f*C;(C||N)&&fg(l,p)&&(S+=N,E+=C);var H=p.parentNode;p=H&&H.nodeType===Node.DOCUMENT_FRAGMENT_NODE?H.host:H}while(!v&&p!==document.body||v&&(u.contains(p)||u===p));return(y&&Math.abs(S)<1||!y&&Math.abs(E)<1)&&(h=!0),h},ci=function(l){return"changedTouches"in l?[l.changedTouches[0].clientX,l.changedTouches[0].clientY]:[0,0]},Hp=function(l){return[l.deltaX,l.deltaY]},Bp=function(l){return l&&"current"in l?l.current:l},pR=function(l,u){return l[0]===u[0]&&l[1]===u[1]},vR=function(l){return`
  .block-interactivity-`.concat(l,` {pointer-events: none;}
  .allow-interactivity-`).concat(l,` {pointer-events: all;}
`)},gR=0,ha=[];function yR(l){var u=g.useRef([]),o=g.useRef([0,0]),i=g.useRef(),s=g.useState(gR++)[0],f=g.useState(cg)[0],m=g.useRef(l);g.useEffect(function(){m.current=l},[l]),g.useEffect(function(){if(l.inert){document.body.classList.add("block-interactivity-".concat(s));var R=kw([l.lockRef.current],(l.shards||[]).map(Bp),!0).filter(Boolean);return R.forEach(function(T){return T.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),R.forEach(function(T){return T.classList.remove("allow-interactivity-".concat(s))})}}},[l.inert,l.lockRef.current,l.shards]);var p=g.useCallback(function(R,T){if("touches"in R&&R.touches.length===2||R.type==="wheel"&&R.ctrlKey)return!m.current.allowPinchZoom;var N=ci(R),H=o.current,k="deltaX"in R?R.deltaX:H[0]-N[0],X="deltaY"in R?R.deltaY:H[1]-N[1],Y,W=R.target,$=Math.abs(k)>Math.abs(X)?"h":"v";if("touches"in R&&$==="h"&&W.type==="range")return!1;var Z=jp($,W);if(!Z)return!0;if(Z?Y=$:(Y=$==="v"?"h":"v",Z=jp($,W)),!Z)return!1;if(!i.current&&"changedTouches"in R&&(k||X)&&(i.current=Y),!Y)return!0;var le=i.current||Y;return hR(le,T,R,le==="h"?k:X)},[]),v=g.useCallback(function(R){var T=R;if(!(!ha.length||ha[ha.length-1]!==f)){var N="deltaY"in T?Hp(T):ci(T),H=u.current.filter(function(Y){return Y.name===T.type&&(Y.target===T.target||T.target===Y.shadowParent)&&pR(Y.delta,N)})[0];if(H&&H.should){T.cancelable&&T.preventDefault();return}if(!H){var k=(m.current.shards||[]).map(Bp).filter(Boolean).filter(function(Y){return Y.contains(T.target)}),X=k.length>0?p(T,k[0]):!m.current.noIsolation;X&&T.cancelable&&T.preventDefault()}}},[]),h=g.useCallback(function(R,T,N,H){var k={name:R,delta:T,target:N,should:H,shadowParent:bR(N)};u.current.push(k),setTimeout(function(){u.current=u.current.filter(function(X){return X!==k})},1)},[]),y=g.useCallback(function(R){o.current=ci(R),i.current=void 0},[]),S=g.useCallback(function(R){h(R.type,Hp(R),R.target,p(R,l.lockRef.current))},[]),E=g.useCallback(function(R){h(R.type,ci(R),R.target,p(R,l.lockRef.current))},[]);g.useEffect(function(){return ha.push(f),l.setCallbacks({onScrollCapture:S,onWheelCapture:S,onTouchMoveCapture:E}),document.addEventListener("wheel",v,ma),document.addEventListener("touchmove",v,ma),document.addEventListener("touchstart",y,ma),function(){ha=ha.filter(function(R){return R!==f}),document.removeEventListener("wheel",v,ma),document.removeEventListener("touchmove",v,ma),document.removeEventListener("touchstart",y,ma)}},[]);var A=l.removeScrollBar,C=l.inert;return g.createElement(g.Fragment,null,C?g.createElement(f,{styles:vR(s)}):null,A?g.createElement(iR,{noRelative:l.noRelative,gapMode:l.gapMode}):null)}function bR(l){for(var u=null;l!==null;)l instanceof ShadowRoot&&(u=l.host,l=l.host),l=l.parentNode;return u}const SR=Pw(og,yR);var mg=g.forwardRef(function(l,u){return g.createElement(_i,$t({},l,{ref:u,sideCar:SR}))});mg.classNames=_i.classNames;var Ms=["Enter"," "],xR=["ArrowDown","PageUp","Home"],hg=["ArrowUp","PageDown","End"],ER=[...xR,...hg],wR={ltr:[...Ms,"ArrowRight"],rtl:[...Ms,"ArrowLeft"]},RR={ltr:["ArrowLeft"],rtl:["ArrowRight"]},kr="Menu",[_r,AR,MR]=wv(kr),[Rl,pg]=Lr(kr,[MR,Vv,eg]),Di=Vv(),vg=eg(),[TR,Al]=Rl(kr),[CR,Gr]=Rl(kr),gg=l=>{const{__scopeMenu:u,open:o=!1,children:i,dir:s,onOpenChange:f,modal:m=!0}=l,p=Di(u),[v,h]=g.useState(null),y=g.useRef(!1),S=xn(f),E=Rv(s);return g.useEffect(()=>{const A=()=>{y.current=!0,document.addEventListener("pointerdown",C,{capture:!0,once:!0}),document.addEventListener("pointermove",C,{capture:!0,once:!0})},C=()=>y.current=!1;return document.addEventListener("keydown",A,{capture:!0}),()=>{document.removeEventListener("keydown",A,{capture:!0}),document.removeEventListener("pointerdown",C,{capture:!0}),document.removeEventListener("pointermove",C,{capture:!0})}},[]),_.jsx(pw,{...p,children:_.jsx(TR,{scope:u,open:o,onOpenChange:S,content:v,onContentChange:h,children:_.jsx(CR,{scope:u,onClose:g.useCallback(()=>S(!1),[S]),isUsingKeyboardRef:y,dir:E,modal:m,children:i})})})};gg.displayName=kr;var OR="MenuAnchor",Ks=g.forwardRef((l,u)=>{const{__scopeMenu:o,...i}=l,s=Di(o);return _.jsx(vw,{...s,...i,ref:u})});Ks.displayName=OR;var Ps="MenuPortal",[_R,yg]=Rl(Ps,{forceMount:void 0}),bg=l=>{const{__scopeMenu:u,forceMount:o,children:i,container:s}=l,f=Al(Ps,u);return _.jsx(_R,{scope:u,forceMount:o,children:_.jsx(Hr,{present:o||f.open,children:_.jsx(Wv,{asChild:!0,container:s,children:i})})})};bg.displayName=Ps;var Ut="MenuContent",[DR,$s]=Rl(Ut),Sg=g.forwardRef((l,u)=>{const o=yg(Ut,l.__scopeMenu),{forceMount:i=o.forceMount,...s}=l,f=Al(Ut,l.__scopeMenu),m=Gr(Ut,l.__scopeMenu);return _.jsx(_r.Provider,{scope:l.__scopeMenu,children:_.jsx(Hr,{present:i||f.open,children:_.jsx(_r.Slot,{scope:l.__scopeMenu,children:m.modal?_.jsx(NR,{...s,ref:u}):_.jsx(zR,{...s,ref:u})})})})}),NR=g.forwardRef((l,u)=>{const o=Al(Ut,l.__scopeMenu),i=g.useRef(null),s=mt(u,i);return g.useEffect(()=>{const f=i.current;if(f)return Bw(f)},[]),_.jsx(Js,{...l,ref:s,trapFocus:o.open,disableOutsidePointerEvents:o.open,disableOutsideScroll:!0,onFocusOutside:Re(l.onFocusOutside,f=>f.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>o.onOpenChange(!1)})}),zR=g.forwardRef((l,u)=>{const o=Al(Ut,l.__scopeMenu);return _.jsx(Js,{...l,ref:u,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>o.onOpenChange(!1)})}),UR=Tr("MenuContent.ScrollLock"),Js=g.forwardRef((l,u)=>{const{__scopeMenu:o,loop:i=!1,trapFocus:s,onOpenAutoFocus:f,onCloseAutoFocus:m,disableOutsidePointerEvents:p,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:y,onFocusOutside:S,onInteractOutside:E,onDismiss:A,disableOutsideScroll:C,...R}=l,T=Al(Ut,o),N=Gr(Ut,o),H=Di(o),k=vg(o),X=AR(o),[Y,W]=g.useState(null),$=g.useRef(null),Z=mt(u,$,T.onContentChange),le=g.useRef(0),se=g.useRef(""),be=g.useRef(0),fe=g.useRef(null),Ee=g.useRef("right"),ge=g.useRef(0),me=C?mg:g.Fragment,z=C?{as:UR,allowPinchZoom:!0}:void 0,K=J=>{const w=se.current+J,V=X().filter(ue=>!ue.disabled),F=document.activeElement,P=V.find(ue=>ue.ref.current===F)?.textValue,I=V.map(ue=>ue.textValue),de=ZR(I,w,P),re=V.find(ue=>ue.textValue===de)?.ref.current;(function ue(Ae){se.current=Ae,window.clearTimeout(le.current),Ae!==""&&(le.current=window.setTimeout(()=>ue(""),1e3))})(w),re&&setTimeout(()=>re.focus())};g.useEffect(()=>()=>window.clearTimeout(le.current),[]),Gx();const B=g.useCallback(J=>Ee.current===fe.current?.side&&PR(J,fe.current?.area),[]);return _.jsx(DR,{scope:o,searchRef:se,onItemEnter:g.useCallback(J=>{B(J)&&J.preventDefault()},[B]),onItemLeave:g.useCallback(J=>{B(J)||($.current?.focus(),W(null))},[B]),onTriggerLeave:g.useCallback(J=>{B(J)&&J.preventDefault()},[B]),pointerGraceTimerRef:be,onPointerGraceIntentChange:g.useCallback(J=>{fe.current=J},[]),children:_.jsx(me,{...z,children:_.jsx(Cv,{asChild:!0,trapped:s,onMountAutoFocus:Re(f,J=>{J.preventDefault(),$.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:m,children:_.jsx(Mv,{asChild:!0,disableOutsidePointerEvents:p,onEscapeKeyDown:h,onPointerDownOutside:y,onFocusOutside:S,onInteractOutside:E,onDismiss:A,children:_.jsx(zw,{asChild:!0,...k,dir:N.dir,orientation:"vertical",loop:i,currentTabStopId:Y,onCurrentTabStopIdChange:W,onEntryFocus:Re(v,J=>{N.isUsingKeyboardRef.current||J.preventDefault()}),preventScrollOnEntryFocus:!0,children:_.jsx(gw,{role:"menu","aria-orientation":"vertical","data-state":jg(T.open),"data-radix-menu-content":"",dir:N.dir,...H,...R,ref:Z,style:{outline:"none",...R.style},onKeyDown:Re(R.onKeyDown,J=>{const V=J.target.closest("[data-radix-menu-content]")===J.currentTarget,F=J.ctrlKey||J.altKey||J.metaKey,P=J.key.length===1;V&&(J.key==="Tab"&&J.preventDefault(),!F&&P&&K(J.key));const I=$.current;if(J.target!==I||!ER.includes(J.key))return;J.preventDefault();const re=X().filter(ue=>!ue.disabled).map(ue=>ue.ref.current);hg.includes(J.key)&&re.reverse(),XR(re)}),onBlur:Re(l.onBlur,J=>{J.currentTarget.contains(J.target)||(window.clearTimeout(le.current),se.current="")}),onPointerMove:Re(l.onPointerMove,Dr(J=>{const w=J.target,V=ge.current!==J.clientX;if(J.currentTarget.contains(w)&&V){const F=J.clientX>ge.current?"right":"left";Ee.current=F,ge.current=J.clientX}}))})})})})})})});Sg.displayName=Ut;var LR="MenuGroup",Fs=g.forwardRef((l,u)=>{const{__scopeMenu:o,...i}=l;return _.jsx(ut.div,{role:"group",...i,ref:u})});Fs.displayName=LR;var jR="MenuLabel",xg=g.forwardRef((l,u)=>{const{__scopeMenu:o,...i}=l;return _.jsx(ut.div,{...i,ref:u})});xg.displayName=jR;var xi="MenuItem",kp="menu.itemSelect",Ni=g.forwardRef((l,u)=>{const{disabled:o=!1,onSelect:i,...s}=l,f=g.useRef(null),m=Gr(xi,l.__scopeMenu),p=$s(xi,l.__scopeMenu),v=mt(u,f),h=g.useRef(!1),y=()=>{const S=f.current;if(!o&&S){const E=new CustomEvent(kp,{bubbles:!0,cancelable:!0});S.addEventListener(kp,A=>i?.(A),{once:!0}),Ev(S,E),E.defaultPrevented?h.current=!1:m.onClose()}};return _.jsx(Eg,{...s,ref:v,disabled:o,onClick:Re(l.onClick,y),onPointerDown:S=>{l.onPointerDown?.(S),h.current=!0},onPointerUp:Re(l.onPointerUp,S=>{h.current||S.currentTarget?.click()}),onKeyDown:Re(l.onKeyDown,S=>{const E=p.searchRef.current!=="";o||E&&S.key===" "||Ms.includes(S.key)&&(S.currentTarget.click(),S.preventDefault())})})});Ni.displayName=xi;var Eg=g.forwardRef((l,u)=>{const{__scopeMenu:o,disabled:i=!1,textValue:s,...f}=l,m=$s(xi,o),p=vg(o),v=g.useRef(null),h=mt(u,v),[y,S]=g.useState(!1),[E,A]=g.useState("");return g.useEffect(()=>{const C=v.current;C&&A((C.textContent??"").trim())},[f.children]),_.jsx(_r.ItemSlot,{scope:o,disabled:i,textValue:s??E,children:_.jsx(Uw,{asChild:!0,...p,focusable:!i,children:_.jsx(ut.div,{role:"menuitem","data-highlighted":y?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...f,ref:h,onPointerMove:Re(l.onPointerMove,Dr(C=>{i?m.onItemLeave(C):(m.onItemEnter(C),C.defaultPrevented||C.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:Re(l.onPointerLeave,Dr(C=>m.onItemLeave(C))),onFocus:Re(l.onFocus,()=>S(!0)),onBlur:Re(l.onBlur,()=>S(!1))})})})}),HR="MenuCheckboxItem",wg=g.forwardRef((l,u)=>{const{checked:o=!1,onCheckedChange:i,...s}=l;return _.jsx(Cg,{scope:l.__scopeMenu,checked:o,children:_.jsx(Ni,{role:"menuitemcheckbox","aria-checked":Ei(o)?"mixed":o,...s,ref:u,"data-state":Is(o),onSelect:Re(s.onSelect,()=>i?.(Ei(o)?!0:!o),{checkForDefaultPrevented:!1})})})});wg.displayName=HR;var Rg="MenuRadioGroup",[BR,kR]=Rl(Rg,{value:void 0,onValueChange:()=>{}}),Ag=g.forwardRef((l,u)=>{const{value:o,onValueChange:i,...s}=l,f=xn(i);return _.jsx(BR,{scope:l.__scopeMenu,value:o,onValueChange:f,children:_.jsx(Fs,{...s,ref:u})})});Ag.displayName=Rg;var Mg="MenuRadioItem",Tg=g.forwardRef((l,u)=>{const{value:o,...i}=l,s=kR(Mg,l.__scopeMenu),f=o===s.value;return _.jsx(Cg,{scope:l.__scopeMenu,checked:f,children:_.jsx(Ni,{role:"menuitemradio","aria-checked":f,...i,ref:u,"data-state":Is(f),onSelect:Re(i.onSelect,()=>s.onValueChange?.(o),{checkForDefaultPrevented:!1})})})});Tg.displayName=Mg;var Ws="MenuItemIndicator",[Cg,GR]=Rl(Ws,{checked:!1}),Og=g.forwardRef((l,u)=>{const{__scopeMenu:o,forceMount:i,...s}=l,f=GR(Ws,o);return _.jsx(Hr,{present:i||Ei(f.checked)||f.checked===!0,children:_.jsx(ut.span,{...s,ref:u,"data-state":Is(f.checked)})})});Og.displayName=Ws;var YR="MenuSeparator",_g=g.forwardRef((l,u)=>{const{__scopeMenu:o,...i}=l;return _.jsx(ut.div,{role:"separator","aria-orientation":"horizontal",...i,ref:u})});_g.displayName=YR;var qR="MenuArrow",Dg=g.forwardRef((l,u)=>{const{__scopeMenu:o,...i}=l,s=Di(o);return _.jsx(yw,{...s,...i,ref:u})});Dg.displayName=qR;var VR="MenuSub",[IA,Ng]=Rl(VR),Ar="MenuSubTrigger",zg=g.forwardRef((l,u)=>{const o=Al(Ar,l.__scopeMenu),i=Gr(Ar,l.__scopeMenu),s=Ng(Ar,l.__scopeMenu),f=$s(Ar,l.__scopeMenu),m=g.useRef(null),{pointerGraceTimerRef:p,onPointerGraceIntentChange:v}=f,h={__scopeMenu:l.__scopeMenu},y=g.useCallback(()=>{m.current&&window.clearTimeout(m.current),m.current=null},[]);return g.useEffect(()=>y,[y]),g.useEffect(()=>{const S=p.current;return()=>{window.clearTimeout(S),v(null)}},[p,v]),_.jsx(Ks,{asChild:!0,...h,children:_.jsx(Eg,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":s.contentId,"data-state":jg(o.open),...l,ref:Ai(u,s.onTriggerChange),onClick:S=>{l.onClick?.(S),!(l.disabled||S.defaultPrevented)&&(S.currentTarget.focus(),o.open||o.onOpenChange(!0))},onPointerMove:Re(l.onPointerMove,Dr(S=>{f.onItemEnter(S),!S.defaultPrevented&&!l.disabled&&!o.open&&!m.current&&(f.onPointerGraceIntentChange(null),m.current=window.setTimeout(()=>{o.onOpenChange(!0),y()},100))})),onPointerLeave:Re(l.onPointerLeave,Dr(S=>{y();const E=o.content?.getBoundingClientRect();if(E){const A=o.content?.dataset.side,C=A==="right",R=C?-5:5,T=E[C?"left":"right"],N=E[C?"right":"left"];f.onPointerGraceIntentChange({area:[{x:S.clientX+R,y:S.clientY},{x:T,y:E.top},{x:N,y:E.top},{x:N,y:E.bottom},{x:T,y:E.bottom}],side:A}),window.clearTimeout(p.current),p.current=window.setTimeout(()=>f.onPointerGraceIntentChange(null),300)}else{if(f.onTriggerLeave(S),S.defaultPrevented)return;f.onPointerGraceIntentChange(null)}})),onKeyDown:Re(l.onKeyDown,S=>{const E=f.searchRef.current!=="";l.disabled||E&&S.key===" "||wR[i.dir].includes(S.key)&&(o.onOpenChange(!0),o.content?.focus(),S.preventDefault())})})})});zg.displayName=Ar;var Ug="MenuSubContent",Lg=g.forwardRef((l,u)=>{const o=yg(Ut,l.__scopeMenu),{forceMount:i=o.forceMount,...s}=l,f=Al(Ut,l.__scopeMenu),m=Gr(Ut,l.__scopeMenu),p=Ng(Ug,l.__scopeMenu),v=g.useRef(null),h=mt(u,v);return _.jsx(_r.Provider,{scope:l.__scopeMenu,children:_.jsx(Hr,{present:i||f.open,children:_.jsx(_r.Slot,{scope:l.__scopeMenu,children:_.jsx(Js,{id:p.contentId,"aria-labelledby":p.triggerId,...s,ref:h,align:"start",side:m.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:y=>{m.isUsingKeyboardRef.current&&v.current?.focus(),y.preventDefault()},onCloseAutoFocus:y=>y.preventDefault(),onFocusOutside:Re(l.onFocusOutside,y=>{y.target!==p.trigger&&f.onOpenChange(!1)}),onEscapeKeyDown:Re(l.onEscapeKeyDown,y=>{m.onClose(),y.preventDefault()}),onKeyDown:Re(l.onKeyDown,y=>{const S=y.currentTarget.contains(y.target),E=RR[m.dir].includes(y.key);S&&E&&(f.onOpenChange(!1),p.trigger?.focus(),y.preventDefault())})})})})})});Lg.displayName=Ug;function jg(l){return l?"open":"closed"}function Ei(l){return l==="indeterminate"}function Is(l){return Ei(l)?"indeterminate":l?"checked":"unchecked"}function XR(l){const u=document.activeElement;for(const o of l)if(o===u||(o.focus(),document.activeElement!==u))return}function QR(l,u){return l.map((o,i)=>l[(u+i)%l.length])}function ZR(l,u,o){const s=u.length>1&&Array.from(u).every(h=>h===u[0])?u[0]:u,f=o?l.indexOf(o):-1;let m=QR(l,Math.max(f,0));s.length===1&&(m=m.filter(h=>h!==o));const v=m.find(h=>h.toLowerCase().startsWith(s.toLowerCase()));return v!==o?v:void 0}function KR(l,u){const{x:o,y:i}=l;let s=!1;for(let f=0,m=u.length-1;f<u.length;m=f++){const p=u[f],v=u[m],h=p.x,y=p.y,S=v.x,E=v.y;y>i!=E>i&&o<(S-h)*(i-y)/(E-y)+h&&(s=!s)}return s}function PR(l,u){if(!u)return!1;const o={x:l.clientX,y:l.clientY};return KR(o,u)}function Dr(l){return u=>u.pointerType==="mouse"?l(u):void 0}var $R=gg,JR=Ks,FR=bg,WR=Sg,IR=Fs,eA=xg,tA=Ni,nA=wg,lA=Ag,aA=Tg,rA=Og,uA=_g,iA=Dg,oA=zg,cA=Lg,zi="DropdownMenu",[sA]=Lr(zi,[pg]),it=pg(),[fA,Hg]=sA(zi),Bg=l=>{const{__scopeDropdownMenu:u,children:o,dir:i,open:s,defaultOpen:f,onOpenChange:m,modal:p=!0}=l,v=it(u),h=g.useRef(null),[y,S]=xv({prop:s,defaultProp:f??!1,onChange:m,caller:zi});return _.jsx(fA,{scope:u,triggerId:Ss(),triggerRef:h,contentId:Ss(),open:y,onOpenChange:S,onOpenToggle:g.useCallback(()=>S(E=>!E),[S]),modal:p,children:_.jsx($R,{...v,open:y,onOpenChange:S,dir:i,modal:p,children:o})})};Bg.displayName=zi;var kg="DropdownMenuTrigger",Gg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,disabled:i=!1,...s}=l,f=Hg(kg,o),m=it(o);return _.jsx(JR,{asChild:!0,...m,children:_.jsx(ut.button,{type:"button",id:f.triggerId,"aria-haspopup":"menu","aria-expanded":f.open,"aria-controls":f.open?f.contentId:void 0,"data-state":f.open?"open":"closed","data-disabled":i?"":void 0,disabled:i,...s,ref:Ai(u,f.triggerRef),onPointerDown:Re(l.onPointerDown,p=>{!i&&p.button===0&&p.ctrlKey===!1&&(f.onOpenToggle(),f.open||p.preventDefault())}),onKeyDown:Re(l.onKeyDown,p=>{i||(["Enter"," "].includes(p.key)&&f.onOpenToggle(),p.key==="ArrowDown"&&f.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(p.key)&&p.preventDefault())})})})});Gg.displayName=kg;var dA="DropdownMenuPortal",Yg=l=>{const{__scopeDropdownMenu:u,...o}=l,i=it(u);return _.jsx(FR,{...i,...o})};Yg.displayName=dA;var qg="DropdownMenuContent",Vg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=Hg(qg,o),f=it(o),m=g.useRef(!1);return _.jsx(WR,{id:s.contentId,"aria-labelledby":s.triggerId,...f,...i,ref:u,onCloseAutoFocus:Re(l.onCloseAutoFocus,p=>{m.current||s.triggerRef.current?.focus(),m.current=!1,p.preventDefault()}),onInteractOutside:Re(l.onInteractOutside,p=>{const v=p.detail.originalEvent,h=v.button===0&&v.ctrlKey===!0,y=v.button===2||h;(!s.modal||y)&&(m.current=!0)}),style:{...l.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Vg.displayName=qg;var mA="DropdownMenuGroup",hA=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(IR,{...s,...i,ref:u})});hA.displayName=mA;var pA="DropdownMenuLabel",Xg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(eA,{...s,...i,ref:u})});Xg.displayName=pA;var vA="DropdownMenuItem",Qg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(tA,{...s,...i,ref:u})});Qg.displayName=vA;var gA="DropdownMenuCheckboxItem",Zg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(nA,{...s,...i,ref:u})});Zg.displayName=gA;var yA="DropdownMenuRadioGroup",bA=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(lA,{...s,...i,ref:u})});bA.displayName=yA;var SA="DropdownMenuRadioItem",Kg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(aA,{...s,...i,ref:u})});Kg.displayName=SA;var xA="DropdownMenuItemIndicator",Pg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(rA,{...s,...i,ref:u})});Pg.displayName=xA;var EA="DropdownMenuSeparator",$g=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(uA,{...s,...i,ref:u})});$g.displayName=EA;var wA="DropdownMenuArrow",RA=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(iA,{...s,...i,ref:u})});RA.displayName=wA;var AA="DropdownMenuSubTrigger",Jg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(oA,{...s,...i,ref:u})});Jg.displayName=AA;var MA="DropdownMenuSubContent",Fg=g.forwardRef((l,u)=>{const{__scopeDropdownMenu:o,...i}=l,s=it(o);return _.jsx(cA,{...s,...i,ref:u,style:{...l.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Fg.displayName=MA;var TA=Bg,CA=Gg,OA=Yg,Wg=Vg,Ig=Xg,ey=Qg,ty=Zg,ny=Kg,ly=Pg,ay=$g,ry=Jg,uy=Fg;const _A=TA,DA=CA,NA=g.forwardRef(({className:l,inset:u,children:o,...i},s)=>_.jsxs(ry,{ref:s,className:et("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",u&&"pl-8",l),...i,children:[o,_.jsx(C1,{className:"ml-auto h-4 w-4"})]}));NA.displayName=ry.displayName;const zA=g.forwardRef(({className:l,...u},o)=>_.jsx(uy,{ref:o,className:et("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",l),...u}));zA.displayName=uy.displayName;const iy=g.forwardRef(({className:l,sideOffset:u=4,...o},i)=>_.jsx(OA,{children:_.jsx(Wg,{ref:i,sideOffset:u,className:et("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",l),...o})}));iy.displayName=Wg.displayName;const pi=g.forwardRef(({className:l,inset:u,...o},i)=>_.jsx(ey,{ref:i,className:et("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",u&&"pl-8",l),...o}));pi.displayName=ey.displayName;const UA=g.forwardRef(({className:l,children:u,checked:o,...i},s)=>_.jsxs(ty,{ref:s,className:et("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),checked:o,...i,children:[_.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:_.jsx(ly,{children:_.jsx(M1,{className:"h-4 w-4"})})}),u]}));UA.displayName=ty.displayName;const LA=g.forwardRef(({className:l,children:u,...o},i)=>_.jsxs(ny,{ref:i,className:et("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),...o,children:[_.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:_.jsx(ly,{children:_.jsx(_1,{className:"h-2 w-2 fill-current"})})}),u]}));LA.displayName=ny.displayName;const jA=g.forwardRef(({className:l,inset:u,...o},i)=>_.jsx(Ig,{ref:i,className:et("px-2 py-1.5 text-sm font-semibold",u&&"pl-8",l),...o}));jA.displayName=Ig.displayName;const HA=g.forwardRef(({className:l,...u},o)=>_.jsx(ay,{ref:o,className:et("-mx-1 my-1 h-px bg-muted",l),...u}));HA.displayName=ay.displayName;function oy(){const{setTheme:l}=Jb();return _.jsxs(_A,{children:[_.jsx(DA,{asChild:!0,children:_.jsxs(Bs,{variant:"outline",size:"icon",children:[_.jsx(j1,{className:"h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90"}),_.jsx(U1,{className:"absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0"}),_.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),_.jsxs(iy,{align:"end",children:[_.jsx(pi,{onClick:()=>l("light"),children:"Light"}),_.jsx(pi,{onClick:()=>l("dark"),children:"Dark"}),_.jsx(pi,{onClick:()=>l("system"),children:"System"})]})]})}const cy=g.forwardRef(({className:l,children:u,...o},i)=>_.jsx("h1",{ref:i,className:l,style:{fontFamily:'"Inter", sans-serif',fontSize:"48px",fontWeight:800,lineHeight:1.2},...o,children:u}));cy.displayName="Heading1";const BA=g.forwardRef(({className:l,children:u,...o},i)=>_.jsx("h2",{ref:i,className:l,style:{fontFamily:'"Inter", sans-serif',fontSize:"40px",fontWeight:700,lineHeight:1.2},...o,children:u}));BA.displayName="Heading2";const kA=g.forwardRef(({className:l,children:u,...o},i)=>_.jsx("h3",{ref:i,className:l,style:{fontFamily:'"Inter", sans-serif',fontSize:"30px",fontWeight:700,lineHeight:1.3},...o,children:u}));kA.displayName="Heading3";const GA=g.forwardRef(({className:l,children:u,...o},i)=>_.jsx("h4",{ref:i,className:l,style:{fontFamily:'"Inter", sans-serif',fontSize:"20px",fontWeight:700,lineHeight:1.4},...o,children:u}));GA.displayName="Heading4";const YA=g.forwardRef(({className:l,children:u,...o},i)=>_.jsx("p",{ref:i,className:l,style:{fontFamily:'"Inter", sans-serif',fontSize:"16px",fontWeight:400,lineHeight:1.5},...o,children:u}));YA.displayName="BodyText";const sy=g.forwardRef(({className:l,children:u,...o},i)=>_.jsx("small",{ref:i,className:l,style:{fontFamily:'"Inter", sans-serif',fontSize:"12px",fontWeight:400,lineHeight:1.4},...o,children:u}));sy.displayName="SmallText";function qA({className:l}){return _.jsx("footer",{className:l,children:_.jsx(sy,{children:"Planfuly Inc"})})}const fy="/logo/Planfuly_Logo.png";function VA({className:l}){return _.jsxs("header",{className:`relative ${l||""}`,children:[_.jsx("div",{className:"absolute top-4 right-4",children:_.jsx(oy,{})}),_.jsx("div",{children:_.jsx("img",{src:fy,className:"logo",alt:"Planfuly logo"})})]})}function XA({children:l,className:u}){return _.jsxs("div",{className:`min-h-svh flex flex-col ${u||""}`,children:[_.jsx(VA,{}),_.jsx("main",{className:"flex-1",children:l}),_.jsx(qA,{})]})}function QA({children:l,className:u}){return _.jsx("div",{className:`min-h-svh flex flex-col ${u||""}`,children:_.jsx("main",{className:"flex-1",children:l})})}const Gp=js("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"",ghost:"border-transparent bg-transparent"},size:{default:"h-10 px-3 py-2",sm:"h-9 px-3",lg:"h-11 px-4"}},defaultVariants:{variant:"default",size:"default"}}),Ts=g.forwardRef(({className:l,variant:u,size:o,type:i,startIcon:s,endIcon:f,...m},p)=>s||f?_.jsxs("div",{className:"relative",children:[s&&_.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground",children:s}),_.jsx("input",{type:i,className:et(Gp({variant:u,size:o,className:l}),s&&"pl-10",f&&"pr-10"),ref:p,...m}),f&&_.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground",children:f})]}):_.jsx("input",{type:i,className:et(Gp({variant:u,size:o,className:l})),ref:p,...m}));Ts.displayName="Input";const ZA=js("rounded-lg border bg-card text-card-foreground shadow-sm",{variants:{variant:{default:"border-border",elevated:"shadow-md",outlined:"border-2"},size:{default:"p-6",sm:"p-4",lg:"p-8",none:"p-0"}},defaultVariants:{variant:"default",size:"default"}}),dy=g.forwardRef(({className:l,variant:u,size:o,...i},s)=>_.jsx("div",{ref:s,className:et(ZA({variant:u,size:o,className:l})),...i}));dy.displayName="Card";const my=g.forwardRef(({className:l,...u},o)=>_.jsx("div",{ref:o,className:et("flex flex-col space-y-1.5 p-6",l),...u}));my.displayName="CardHeader";const hy=g.forwardRef(({className:l,...u},o)=>_.jsx("h3",{ref:o,className:et("text-2xl font-semibold leading-none tracking-tight",l),...u}));hy.displayName="CardTitle";const KA=g.forwardRef(({className:l,...u},o)=>_.jsx("p",{ref:o,className:et("text-sm text-muted-foreground",l),...u}));KA.displayName="CardDescription";const py=g.forwardRef(({className:l,...u},o)=>_.jsx("div",{ref:o,className:et("p-6 pt-0",l),...u}));py.displayName="CardContent";const PA=g.forwardRef(({className:l,...u},o)=>_.jsx("div",{ref:o,className:et("flex items-center p-6 pt-0",l),...u}));PA.displayName="CardFooter";function $A({onLogin:l}){return _.jsxs(dy,{className:"w-full max-w-md mx-4 relative shadow-lg",children:[_.jsx("div",{className:"absolute -top-28 left-1/2 transform -translate-x-1/2",children:_.jsx("div",{className:"bg-white rounded-t-lg flex items-center justify-center",children:_.jsx("img",{src:fy,className:"h-30 w-auto m-6",alt:"Planfuly logo"})})}),_.jsx(my,{className:"pt-12 pb-6",children:_.jsx(hy,{className:"text-center",children:"Login"})}),_.jsxs(py,{className:"space-y-6",children:[_.jsxs("div",{className:"space-y-4",children:[_.jsx(Ts,{type:"text",placeholder:"Username",startIcon:_.jsx(B1,{className:"h-4 w-4"}),className:"h-12"}),_.jsx(Ts,{type:"password",placeholder:"Password",startIcon:_.jsx(N1,{className:"h-4 w-4"}),className:"h-12"})]}),_.jsx(Bs,{onClick:l,className:"w-full h-12 text-base font-medium",size:"lg",children:"Login"})]})]})}function JA(){const l=_s(),u=()=>{l("/dashboard")};return _.jsx(QA,{children:_.jsxs("div",{className:"relative min-h-screen flex items-center justify-center bg-background",children:[_.jsx("div",{className:"absolute top-4 right-4",children:_.jsx(oy,{})}),_.jsx($A,{onLogin:u})]})})}function FA(){const l=_s(),u=()=>{l("/")};return _.jsx(XA,{children:_.jsxs("div",{children:[_.jsx(cy,{children:"Dashboard"}),_.jsxs("div",{className:"flex flex-col items-center justify-center flex-1",children:[_.jsx("div",{className:"text-center mb-8",children:_.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Hello World"})}),_.jsx(Bs,{onClick:u,children:"Back to Main"})]})]})})}function WA(){return _.jsx($b,{defaultTheme:"system",storageKey:"planfuly-ui-theme",children:_.jsx(c1,{children:_.jsxs(GS,{children:[_.jsx(hs,{path:"/",element:_.jsx(JA,{})}),_.jsx(hs,{path:"/dashboard",element:_.jsx(FA,{})})]})})})}Kb.createRoot(document.getElementById("root")).render(_.jsx(g.StrictMode,{children:_.jsx(WA,{})}));
