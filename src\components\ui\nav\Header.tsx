import { ModeToggle } from "@/components/mode-toggle"
import planfulyLogo from '/logo/Planfuly_Logo.png'

interface HeaderProps {
  className?: string
}

export function Header({ className }: HeaderProps) {
  return (
    <header className={`relative ${className || ""}`}>
      <div className="absolute top-4 right-4">
        <ModeToggle />
      </div>
      
      <div>
        <img src={planfulyLogo} className="logo" alt="Planfuly logo" />
      </div>
    </header>
  )
}
