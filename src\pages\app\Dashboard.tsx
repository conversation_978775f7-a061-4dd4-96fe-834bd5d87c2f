import { But<PERSON> } from "@/components/ui/basic/button"
import { Heading1 } from "@/components/ui/typography"
import { Layout } from "@/components/ui/layout"
import { useNavigate } from "react-router-dom"

function Dashboard() {
  const navigate = useNavigate()

  const handleBackToMain = () => {
    navigate('/')
  }

  return (
    <Layout>
      <div>
        <Heading1>Dashboard</Heading1>

        <div className="flex flex-col items-center justify-center flex-1">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-semibold mb-4">Hello World</h2>
          </div>
          <Button onClick={handleBackToMain}>Back to Main</Button>
        </div>
      </div>
    </Layout>
  )
}

export default Dashboard
