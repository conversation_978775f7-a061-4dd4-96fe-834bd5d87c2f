import type { ReactNode } from "react"
import { Footer } from "@/components/ui/nav/Footer"
import { Header } from "@/components/ui/nav/Header"

interface LayoutProps {
  children: ReactNode
  className?: string
}

export function Layout({ children, className }: LayoutProps) {
  return (
    <div className={`min-h-svh flex flex-col ${className || ""}`}>
      <Header />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  )
}
