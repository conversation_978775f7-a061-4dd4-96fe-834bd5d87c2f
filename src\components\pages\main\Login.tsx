import { But<PERSON> } from "@/components/ui/basic/button"
import { Input } from "@/components/ui/form/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/container/card"
import planfulyLogo from '/logo/Planfuly_Logo.png'
import { <PERSON><PERSON>, <PERSON> } from "lucide-react"

interface LoginProps {
  onLogin: () => void
}

export function Login({ onLogin }: LoginProps) {
  return (
    <Card className="w-full max-w-md mx-4 relative shadow-lg">
      {/* Logo Tab */}
      <div className="absolute -top-28 left-1/2 transform -translate-x-1/2">
        <div className="bg-white rounded-t-lg flex items-center justify-center">
          <img
            src={planfulyLogo}
            className="h-30 w-auto m-6"
            alt="Planfuly logo"
          />
        </div>
      </div>

      <CardHeader className="pt-12 pb-6">
        <CardTitle className="text-center">Login</CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Input
            type="text"
            placeholder="Username"
            startIcon={<User className="h-4 w-4" />}
            className="h-12"
          />

          <Input
            type="password"
            placeholder="Password"
            startIcon={<Lock className="h-4 w-4" />}
            className="h-12"
          />
        </div>

        <Button
          onClick={onLogin}
          className="w-full h-12 text-base font-medium"
          size="lg"
        >
          Login
        </Button>
      </CardContent>
    </Card>
  )
}
